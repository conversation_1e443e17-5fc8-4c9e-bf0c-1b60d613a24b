<template>
  <div
    ref="sidebarRef"
    class="navigation-sidebar flex flex-col w-15 bg-[#e4e7ed] shadow-md h-screen"
  >
    <!-- 导航按钮列表 -->
    <nav ref="navRef" class="flex flex-col items-center py-4 space-y-3 flex-1 overflow-hidden">
      <!-- 显示的导航按钮 -->
      <button
        v-for="item in visibleNavItems"
        :key="item.id"
        @click="setActiveItem(item.id)"
        :class="[
          'relative flex flex-col items-center justify-center w-14 h-14 rounded-xl transition-all duration-200',
          activeItem === item.id
            ? 'bg-[#d8dae0] text-gray-700'
            : 'text-gray-600 hover:bg-[#d8dae0] hover:text-gray-700'
        ]"
      >
        <img :src="item.icon" :alt="item.label" class="w-5 h-5 mb-1" />
        <span class="text-xs font-medium">{{ item.label }}</span>
      </button>

      <!-- 更多按钮 -->
      <a-popover
        v-if="hiddenNavItems.length > 0"
        v-model:open="showMorePopover"
        placement="topLeft"
        trigger="click"
      >
        <template #content>
          <div class="min-w-[120px] max-h-[300px] overflow-y-auto">
            <button
              v-for="item in hiddenNavItems"
              :key="item.id"
              @click="setActiveItem(item.id)"
              :class="[
                'w-full flex items-center gap-3 px-3 py-2 text-left text-sm transition-colors border-none bg-transparent cursor-pointer',
                activeItem === item.id
                  ? 'bg-[#d8dae0] text-gray-700'
                  : 'text-gray-600 hover:bg-[#d8dae0] hover:text-gray-700'
              ]"
            >
              <img :src="item.icon" :alt="item.label" class="w-4 h-4" />
              <span>{{ item.label }}</span>
            </button>
          </div>
        </template>

        <button
          :class="[
            'relative flex flex-col items-center justify-center w-14 h-14 rounded-xl transition-all duration-200',
            'text-gray-600 hover:bg-[#d8dae0] hover:text-gray-700'
          ]"
        >
          <svg class="w-5 h-5 mb-1" fill="currentColor" viewBox="0 0 20 20">
            <path
              d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
            />
          </svg>
          <span class="text-xs font-medium">更多</span>
        </button>
      </a-popover>
    </nav>

    <!-- 底部用户头像 -->
    <div class="mb-4 flex justify-center flex-shrink-0">
      <a-popover v-model:open="showUserPopover" placement="topLeft" trigger="click">
        <template #content>
          <div class="min-w-[140px]">
            <div class="px-3 py-2 border-b border-gray-100">
              <div class="text-sm font-medium text-gray-900">{{ userDisplayName }}</div>
              <div class="text-xs text-gray-500">{{ userEmail }}</div>
            </div>
            <button
              @click="handleLogout"
              class="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 transition-colors border-none bg-transparent cursor-pointer"
            >
              退出登录
            </button>
          </div>
        </template>

        <div class="relative">
          <button
            class="w-8 h-8 rounded-lg flex items-center justify-center text-white font-medium text-xs transition-all duration-200 hover:opacity-80"
            :style="{ backgroundColor: userAvatarColor }"
          >
            {{ userAvatar }}
          </button>
          <!-- PC图标叠加层 -->
          <div
            class="absolute -bottom-1 -right-1 w-4 h-4 bg-white rounded-full flex items-center justify-center shadow-sm"
          >
            <img :src="pcIconUrl" alt="PC" class="w-3 h-3" />
          </div>
        </div>
      </a-popover>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useUserStore } from '../store/user'
import { useMessageStore } from '../store/message'
import { generateAvatarColor } from '../utils/avatarColors'

// 用户状态管理
const userStore = useUserStore()
const messageStore = useMessageStore()

// DOM 引用
const sidebarRef = ref<HTMLElement>()
const navRef = ref<HTMLElement>()

// 弹出菜单状态
const showUserPopover = ref(false)
const showMorePopover = ref(false)

// 响应式状态
const maxVisibleItems = ref(8) // 默认显示的最大导航项数量

// PC图标URL
const pcIconUrl = new URL('../assets/navigation-icons/pc.svg', import.meta.url).href

// 计算用户头像显示文字
const userAvatar = computed(() => {
  const user = userStore.currentUser.value
  if (user?.displayName) {
    // 获取姓名的后两个字作为头像
    return user.displayName.length >= 2 ? user.displayName.slice(-2) : user.displayName
  }
  return '用户'
})

// 计算用户头像颜色
const userAvatarColor = computed(() => {
  const user = userStore.currentUser.value
  if (user?.displayName) {
    return generateAvatarColor(user.displayName)
  }
  return '#3b82f6' // 默认蓝色
})

// 计算用户显示名称
const userDisplayName = computed(() => {
  return userStore.userDisplayName.value || '用户'
})

// 计算用户邮箱
const userEmail = computed(() => {
  return userStore.currentUser.value?.email || ''
})

// 导航项配置
const navItems = [
  {
    id: 'search',
    label: '搜索',
    icon: new URL('../assets/navigation-icons/search.png', import.meta.url).href
  },
  {
    id: 'documents',
    label: '文档',
    icon: new URL('../assets/navigation-icons/document.png', import.meta.url).href
  },
  {
    id: 'calendar',
    label: '日历',
    icon: new URL('../assets/navigation-icons/calendar.png', import.meta.url).href
  },
  {
    id: 'meetings',
    label: '会议',
    icon: new URL('../assets/navigation-icons/meeting.png', import.meta.url).href
  },
  {
    id: 'mail',
    label: '邮箱',
    icon: new URL('../assets/navigation-icons/mailbox.png', import.meta.url).href
  },
  {
    id: 'workspace',
    label: '工作台',
    icon: new URL('../assets/navigation-icons/workbench.png', import.meta.url).href
  },
  {
    id: 'assistant',
    label: '助手',
    icon: new URL('../assets/navigation-icons/assistant.png', import.meta.url).href
  },
  {
    id: 'profile',
    label: '我的',
    icon: new URL('../assets/navigation-icons/profile.png', import.meta.url).href
  }
]

// 计算可见的导航项
const visibleNavItems = computed(() => {
  return navItems.slice(0, maxVisibleItems.value)
})

// 计算隐藏的导航项
const hiddenNavItems = computed(() => {
  return navItems.slice(maxVisibleItems.value)
})

// 当前激活的导航项
const activeItem = ref('')

// 设置激活项
const setActiveItem = (itemId: string) => {
  activeItem.value = itemId
  // 关闭更多菜单
  showMorePopover.value = false
  // 发出事件给父组件
  emit('nav-change', itemId)
}

// 处理退出登录
const handleLogout = () => {
  userStore.logout()
  messageStore.disconnectWebSocket()
  showUserPopover.value = false
  // 触发页面重新加载，显示登录页面
  window.location.reload()
}

// 点击外部取消激活状态
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  const navigationSidebar = target.closest('.navigation-sidebar')

  // 如果点击的不是导航栏内部，取消激活状态
  if (!navigationSidebar) {
    activeItem.value = ''
  }
}

// 计算可显示的导航项数量
const calculateMaxVisibleItems = () => {
  if (!sidebarRef.value) return

  const sidebarHeight = sidebarRef.value.clientHeight
  const navPadding = 32 // py-4 = 16px * 2
  const itemHeight = 68 // w-14 h-14 (56px) + space-y-3 (12px)
  const avatarAreaHeight = 64 // 底部头像区域高度 (32px + 16px margin)
  const reservedSpace = 20 // 预留空间

  // 可用于导航按钮的高度
  const availableHeight = sidebarHeight - navPadding - avatarAreaHeight - reservedSpace

  // 计算最多可以显示多少个导航项（不包括更多按钮）
  let maxItems = Math.floor(availableHeight / itemHeight)

  // 如果所有导航项都能显示，就不需要更多按钮
  if (maxItems >= navItems.length) {
    maxVisibleItems.value = navItems.length
    return
  }

  // 如果需要更多按钮，需要为更多按钮预留空间
  const maxItemsWithMoreButton = Math.floor((availableHeight - itemHeight) / itemHeight)

  // 确保至少显示1个导航项
  maxVisibleItems.value = Math.max(1, maxItemsWithMoreButton)
}

// ResizeObserver 来监听容器大小变化
let resizeObserver: ResizeObserver | null = null

// 组件挂载时的初始化
onMounted(() => {
  // 初始计算
  nextTick(() => {
    calculateMaxVisibleItems()
  })

  // 监听容器大小变化
  if (sidebarRef.value) {
    resizeObserver = new ResizeObserver(() => {
      calculateMaxVisibleItems()
    })
    resizeObserver.observe(sidebarRef.value)
  }

  // 添加点击外部事件监听器
  document.addEventListener('click', handleClickOutside)
})

// 组件卸载时清理
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  document.removeEventListener('click', handleClickOutside)
})

// 定义事件
const emit = defineEmits<{
  'nav-change': [itemId: string]
}>()
</script>
