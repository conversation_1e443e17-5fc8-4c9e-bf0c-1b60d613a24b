{"version": 3, "sources": ["../../.pnpm/@protobufjs+aspromise@1.1.2/node_modules/@protobufjs/aspromise/index.js", "../../.pnpm/@protobufjs+base64@1.1.2/node_modules/@protobufjs/base64/index.js", "../../.pnpm/@protobufjs+eventemitter@1.1.0/node_modules/@protobufjs/eventemitter/index.js", "../../.pnpm/@protobufjs+float@1.0.2/node_modules/@protobufjs/float/index.js", "../../.pnpm/@protobufjs+inquire@1.1.0/node_modules/@protobufjs/inquire/index.js", "../../.pnpm/@protobufjs+utf8@1.1.0/node_modules/@protobufjs/utf8/index.js", "../../.pnpm/@protobufjs+pool@1.1.0/node_modules/@protobufjs/pool/index.js", "../../.pnpm/protobufjs@7.5.4/node_modules/protobufjs/src/util/longbits.js", "../../.pnpm/protobufjs@7.5.4/node_modules/protobufjs/src/util/minimal.js", "../../.pnpm/protobufjs@7.5.4/node_modules/protobufjs/src/writer.js", "../../.pnpm/protobufjs@7.5.4/node_modules/protobufjs/src/writer_buffer.js", "../../.pnpm/protobufjs@7.5.4/node_modules/protobufjs/src/reader.js", "../../.pnpm/protobufjs@7.5.4/node_modules/protobufjs/src/reader_buffer.js", "../../.pnpm/protobufjs@7.5.4/node_modules/protobufjs/src/rpc/service.js", "../../.pnpm/protobufjs@7.5.4/node_modules/protobufjs/src/rpc.js", "../../.pnpm/protobufjs@7.5.4/node_modules/protobufjs/src/roots.js", "../../.pnpm/protobufjs@7.5.4/node_modules/protobufjs/src/index-minimal.js", "../../.pnpm/protobufjs@7.5.4/node_modules/protobufjs/minimal.js"], "sourcesContent": ["\"use strict\";\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n", "\"use strict\";\nmodule.exports = LongBits;\n\nvar util = require(\"../util/minimal\");\n\n/**\n * Constructs new long bits.\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\n * @memberof util\n * @constructor\n * @param {number} lo Low 32 bits, unsigned\n * @param {number} hi High 32 bits, unsigned\n */\nfunction LongBits(lo, hi) {\n\n    // note that the casts below are theoretically unnecessary as of today, but older statically\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\n\n    /**\n     * Low bits.\n     * @type {number}\n     */\n    this.lo = lo >>> 0;\n\n    /**\n     * High bits.\n     * @type {number}\n     */\n    this.hi = hi >>> 0;\n}\n\n/**\n * Zero bits.\n * @memberof util.LongBits\n * @type {util.LongBits}\n */\nvar zero = LongBits.zero = new LongBits(0, 0);\n\nzero.toNumber = function() { return 0; };\nzero.zzEncode = zero.zzDecode = function() { return this; };\nzero.length = function() { return 1; };\n\n/**\n * Zero hash.\n * @memberof util.LongBits\n * @type {string}\n */\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\n\n/**\n * Constructs new long bits from the specified number.\n * @param {number} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.fromNumber = function fromNumber(value) {\n    if (value === 0)\n        return zero;\n    var sign = value < 0;\n    if (sign)\n        value = -value;\n    var lo = value >>> 0,\n        hi = (value - lo) / 4294967296 >>> 0;\n    if (sign) {\n        hi = ~hi >>> 0;\n        lo = ~lo >>> 0;\n        if (++lo > 4294967295) {\n            lo = 0;\n            if (++hi > 4294967295)\n                hi = 0;\n        }\n    }\n    return new LongBits(lo, hi);\n};\n\n/**\n * Constructs new long bits from a number, long or string.\n * @param {Long|number|string} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.from = function from(value) {\n    if (typeof value === \"number\")\n        return LongBits.fromNumber(value);\n    if (util.isString(value)) {\n        /* istanbul ignore else */\n        if (util.Long)\n            value = util.Long.fromString(value);\n        else\n            return LongBits.fromNumber(parseInt(value, 10));\n    }\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\n};\n\n/**\n * Converts this long bits to a possibly unsafe JavaScript number.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {number} Possibly unsafe number\n */\nLongBits.prototype.toNumber = function toNumber(unsigned) {\n    if (!unsigned && this.hi >>> 31) {\n        var lo = ~this.lo + 1 >>> 0,\n            hi = ~this.hi     >>> 0;\n        if (!lo)\n            hi = hi + 1 >>> 0;\n        return -(lo + hi * 4294967296);\n    }\n    return this.lo + this.hi * 4294967296;\n};\n\n/**\n * Converts this long bits to a long.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long} Long\n */\nLongBits.prototype.toLong = function toLong(unsigned) {\n    return util.Long\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\n        /* istanbul ignore next */\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\n};\n\nvar charCodeAt = String.prototype.charCodeAt;\n\n/**\n * Constructs new long bits from the specified 8 characters long hash.\n * @param {string} hash Hash\n * @returns {util.LongBits} Bits\n */\nLongBits.fromHash = function fromHash(hash) {\n    if (hash === zeroHash)\n        return zero;\n    return new LongBits(\n        ( charCodeAt.call(hash, 0)\n        | charCodeAt.call(hash, 1) << 8\n        | charCodeAt.call(hash, 2) << 16\n        | charCodeAt.call(hash, 3) << 24) >>> 0\n    ,\n        ( charCodeAt.call(hash, 4)\n        | charCodeAt.call(hash, 5) << 8\n        | charCodeAt.call(hash, 6) << 16\n        | charCodeAt.call(hash, 7) << 24) >>> 0\n    );\n};\n\n/**\n * Converts this long bits to a 8 characters long hash.\n * @returns {string} Hash\n */\nLongBits.prototype.toHash = function toHash() {\n    return String.fromCharCode(\n        this.lo        & 255,\n        this.lo >>> 8  & 255,\n        this.lo >>> 16 & 255,\n        this.lo >>> 24      ,\n        this.hi        & 255,\n        this.hi >>> 8  & 255,\n        this.hi >>> 16 & 255,\n        this.hi >>> 24\n    );\n};\n\n/**\n * Zig-zag encodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzEncode = function zzEncode() {\n    var mask =   this.hi >> 31;\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Zig-zag decodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzDecode = function zzDecode() {\n    var mask = -(this.lo & 1);\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Calculates the length of this longbits when encoded as a varint.\n * @returns {number} Length\n */\nLongBits.prototype.length = function length() {\n    var part0 =  this.lo,\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\n        part2 =  this.hi >>> 24;\n    return part2 === 0\n         ? part1 === 0\n           ? part0 < 16384\n             ? part0 < 128 ? 1 : 2\n             : part0 < 2097152 ? 3 : 4\n           : part1 < 16384\n             ? part1 < 128 ? 5 : 6\n             : part1 < 2097152 ? 7 : 8\n         : part2 < 128 ? 9 : 10;\n};\n", "\"use strict\";\nvar util = exports;\n\n// used to return a Promise where callback is omitted\nutil.asPromise = require(\"@protobufjs/aspromise\");\n\n// converts to / from base64 encoded strings\nutil.base64 = require(\"@protobufjs/base64\");\n\n// base class of rpc.Service\nutil.EventEmitter = require(\"@protobufjs/eventemitter\");\n\n// float handling accross browsers\nutil.float = require(\"@protobufjs/float\");\n\n// requires modules optionally and hides the call from bundlers\nutil.inquire = require(\"@protobufjs/inquire\");\n\n// converts to / from utf8 encoded strings\nutil.utf8 = require(\"@protobufjs/utf8\");\n\n// provides a node-like buffer pool in the browser\nutil.pool = require(\"@protobufjs/pool\");\n\n// utility to work with the low and high bits of a 64 bit value\nutil.LongBits = require(\"./longbits\");\n\n/**\n * Whether running within node or not.\n * @memberof util\n * @type {boolean}\n */\nutil.isNode = Boolean(typeof global !== \"undefined\"\n                   && global\n                   && global.process\n                   && global.process.versions\n                   && global.process.versions.node);\n\n/**\n * Global object reference.\n * @memberof util\n * @type {Object}\n */\nutil.global = util.isNode && global\n           || typeof window !== \"undefined\" && window\n           || typeof self   !== \"undefined\" && self\n           || this; // eslint-disable-line no-invalid-this\n\n/**\n * An immuable empty array.\n * @memberof util\n * @type {Array.<*>}\n * @const\n */\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\n\n/**\n * An immutable empty object.\n * @type {Object}\n * @const\n */\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\n\n/**\n * Tests if the specified value is an integer.\n * @function\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is an integer\n */\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n};\n\n/**\n * Tests if the specified value is a string.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a string\n */\nutil.isString = function isString(value) {\n    return typeof value === \"string\" || value instanceof String;\n};\n\n/**\n * Tests if the specified value is a non-null object.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a non-null object\n */\nutil.isObject = function isObject(value) {\n    return value && typeof value === \"object\";\n};\n\n/**\n * Checks if a property on a message is considered to be present.\n * This is an alias of {@link util.isSet}.\n * @function\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isset =\n\n/**\n * Checks if a property on a message is considered to be present.\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isSet = function isSet(obj, prop) {\n    var value = obj[prop];\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\n    return false;\n};\n\n/**\n * Any compatible Buffer instance.\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\n * @interface Buffer\n * @extends Uint8Array\n */\n\n/**\n * Node's Buffer class if available.\n * @type {Constructor<Buffer>}\n */\nutil.Buffer = (function() {\n    try {\n        var Buffer = util.inquire(\"buffer\").Buffer;\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\n    } catch (e) {\n        /* istanbul ignore next */\n        return null;\n    }\n})();\n\n// Internal alias of or polyfull for Buffer.from.\nutil._Buffer_from = null;\n\n// Internal alias of or polyfill for Buffer.allocUnsafe.\nutil._Buffer_allocUnsafe = null;\n\n/**\n * Creates a new buffer of whatever type supported by the environment.\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\n * @returns {Uint8Array|Buffer} Buffer\n */\nutil.newBuffer = function newBuffer(sizeOrArray) {\n    /* istanbul ignore next */\n    return typeof sizeOrArray === \"number\"\n        ? util.Buffer\n            ? util._Buffer_allocUnsafe(sizeOrArray)\n            : new util.Array(sizeOrArray)\n        : util.Buffer\n            ? util._Buffer_from(sizeOrArray)\n            : typeof Uint8Array === \"undefined\"\n                ? sizeOrArray\n                : new Uint8Array(sizeOrArray);\n};\n\n/**\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\n * @type {Constructor<Uint8Array>}\n */\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\n\n/**\n * Any compatible Long instance.\n * This is a minimal stand-alone definition of a Long instance. The actual type is that exported by long.js.\n * @interface Long\n * @property {number} low Low bits\n * @property {number} high High bits\n * @property {boolean} unsigned Whether unsigned or not\n */\n\n/**\n * Long.js's Long class if available.\n * @type {Constructor<Long>}\n */\nutil.Long = /* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\n         || /* istanbul ignore next */ util.global.Long\n         || util.inquire(\"long\");\n\n/**\n * Regular expression used to verify 2 bit (`bool`) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key2Re = /^true|false|0|1$/;\n\n/**\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\n\n/**\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\n\n/**\n * Converts a number or long to an 8 characters long hash string.\n * @param {Long|number} value Value to convert\n * @returns {string} Hash\n */\nutil.longToHash = function longToHash(value) {\n    return value\n        ? util.LongBits.from(value).toHash()\n        : util.LongBits.zeroHash;\n};\n\n/**\n * Converts an 8 characters long hash string to a long or number.\n * @param {string} hash Hash\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long|number} Original value\n */\nutil.longFromHash = function longFromHash(hash, unsigned) {\n    var bits = util.LongBits.fromHash(hash);\n    if (util.Long)\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\n    return bits.toNumber(Boolean(unsigned));\n};\n\n/**\n * Merges the properties of the source object into the destination object.\n * @memberof util\n * @param {Object.<string,*>} dst Destination object\n * @param {Object.<string,*>} src Source object\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\n * @returns {Object.<string,*>} Destination object\n */\nfunction merge(dst, src, ifNotSet) { // used by converters\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\n        if (dst[keys[i]] === undefined || !ifNotSet)\n            dst[keys[i]] = src[keys[i]];\n    return dst;\n}\n\nutil.merge = merge;\n\n/**\n * Converts the first character of a string to lower case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.lcFirst = function lcFirst(str) {\n    return str.charAt(0).toLowerCase() + str.substring(1);\n};\n\n/**\n * Creates a custom error constructor.\n * @memberof util\n * @param {string} name Error name\n * @returns {Constructor<Error>} Custom error constructor\n */\nfunction newError(name) {\n\n    function CustomError(message, properties) {\n\n        if (!(this instanceof CustomError))\n            return new CustomError(message, properties);\n\n        // Error.call(this, message);\n        // ^ just returns a new error instance because the ctor can be called as a function\n\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\n\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) // node\n            Error.captureStackTrace(this, CustomError);\n        else\n            Object.defineProperty(this, \"stack\", { value: new Error().stack || \"\" });\n\n        if (properties)\n            merge(this, properties);\n    }\n\n    CustomError.prototype = Object.create(Error.prototype, {\n        constructor: {\n            value: CustomError,\n            writable: true,\n            enumerable: false,\n            configurable: true,\n        },\n        name: {\n            get: function get() { return name; },\n            set: undefined,\n            enumerable: false,\n            // configurable: false would accurately preserve the behavior of\n            // the original, but I'm guessing that was not intentional.\n            // For an actual error subclass, this property would\n            // be configurable.\n            configurable: true,\n        },\n        toString: {\n            value: function value() { return this.name + \": \" + this.message; },\n            writable: true,\n            enumerable: false,\n            configurable: true,\n        },\n    });\n\n    return CustomError;\n}\n\nutil.newError = newError;\n\n/**\n * Constructs a new protocol error.\n * @classdesc Error subclass indicating a protocol specifc error.\n * @memberof util\n * @extends Error\n * @template T extends Message<T>\n * @constructor\n * @param {string} message Error message\n * @param {Object.<string,*>} [properties] Additional properties\n * @example\n * try {\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\n * } catch (e) {\n *     if (e instanceof ProtocolError && e.instance)\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\n * }\n */\nutil.ProtocolError = newError(\"ProtocolError\");\n\n/**\n * So far decoded message instance.\n * @name util.ProtocolError#instance\n * @type {Message<T>}\n */\n\n/**\n * A OneOf getter as returned by {@link util.oneOfGetter}.\n * @typedef OneOfGetter\n * @type {function}\n * @returns {string|undefined} Set field name, if any\n */\n\n/**\n * Builds a getter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfGetter} Unbound getter\n */\nutil.oneOfGetter = function getOneOf(fieldNames) {\n    var fieldMap = {};\n    for (var i = 0; i < fieldNames.length; ++i)\n        fieldMap[fieldNames[i]] = 1;\n\n    /**\n     * @returns {string|undefined} Set field name, if any\n     * @this Object\n     * @ignore\n     */\n    return function() { // eslint-disable-line consistent-return\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\n                return keys[i];\n    };\n};\n\n/**\n * A OneOf setter as returned by {@link util.oneOfSetter}.\n * @typedef OneOfSetter\n * @type {function}\n * @param {string|undefined} value Field name\n * @returns {undefined}\n */\n\n/**\n * Builds a setter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfSetter} Unbound setter\n */\nutil.oneOfSetter = function setOneOf(fieldNames) {\n\n    /**\n     * @param {string} name Field name\n     * @returns {undefined}\n     * @this Object\n     * @ignore\n     */\n    return function(name) {\n        for (var i = 0; i < fieldNames.length; ++i)\n            if (fieldNames[i] !== name)\n                delete this[fieldNames[i]];\n    };\n};\n\n/**\n * Default conversion options used for {@link Message#toJSON} implementations.\n *\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\n *\n * - Longs become strings\n * - Enums become string keys\n * - Bytes become base64 encoded strings\n * - (Sub-)Messages become plain objects\n * - Maps become plain objects with all string keys\n * - Repeated fields become arrays\n * - NaN and Infinity for float and double fields become strings\n *\n * @type {IConversionOptions}\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\n */\nutil.toJSONOptions = {\n    longs: String,\n    enums: String,\n    bytes: String,\n    json: true\n};\n\n// Sets up buffer utility according to the environment (called in index-minimal)\nutil._configure = function() {\n    var Buffer = util.Buffer;\n    /* istanbul ignore if */\n    if (!Buffer) {\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\n        return;\n    }\n    // because node 4.x buffers are incompatible & immutable\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\n        /* istanbul ignore next */\n        function Buffer_from(value, encoding) {\n            return new Buffer(value, encoding);\n        };\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\n        /* istanbul ignore next */\n        function Buffer_allocUnsafe(size) {\n            return new Buffer(size);\n        };\n};\n", "\"use strict\";\nmodule.exports = Writer;\n\nvar util      = require(\"./util/minimal\");\n\nvar BufferWriter; // cyclic\n\nvar LongBits  = util.LongBits,\n    base64    = util.base64,\n    utf8      = util.utf8;\n\n/**\n * Constructs a new writer operation instance.\n * @classdesc Scheduled writer operation.\n * @constructor\n * @param {function(*, Uint8Array, number)} fn Function to call\n * @param {number} len Value byte length\n * @param {*} val Value to write\n * @ignore\n */\nfunction Op(fn, len, val) {\n\n    /**\n     * Function to call.\n     * @type {function(Uint8Array, number, *)}\n     */\n    this.fn = fn;\n\n    /**\n     * Value byte length.\n     * @type {number}\n     */\n    this.len = len;\n\n    /**\n     * Next operation.\n     * @type {Writer.Op|undefined}\n     */\n    this.next = undefined;\n\n    /**\n     * Value to write.\n     * @type {*}\n     */\n    this.val = val; // type varies\n}\n\n/* istanbul ignore next */\nfunction noop() {} // eslint-disable-line no-empty-function\n\n/**\n * Constructs a new writer state instance.\n * @classdesc Copied writer state.\n * @memberof Writer\n * @constructor\n * @param {Writer} writer Writer to copy state from\n * @ignore\n */\nfunction State(writer) {\n\n    /**\n     * Current head.\n     * @type {Writer.Op}\n     */\n    this.head = writer.head;\n\n    /**\n     * Current tail.\n     * @type {Writer.Op}\n     */\n    this.tail = writer.tail;\n\n    /**\n     * Current buffer length.\n     * @type {number}\n     */\n    this.len = writer.len;\n\n    /**\n     * Next state.\n     * @type {State|null}\n     */\n    this.next = writer.states;\n}\n\n/**\n * Constructs a new writer instance.\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n */\nfunction Writer() {\n\n    /**\n     * Current length.\n     * @type {number}\n     */\n    this.len = 0;\n\n    /**\n     * Operations head.\n     * @type {Object}\n     */\n    this.head = new Op(noop, 0, 0);\n\n    /**\n     * Operations tail\n     * @type {Object}\n     */\n    this.tail = this.head;\n\n    /**\n     * Linked forked states.\n     * @type {Object|null}\n     */\n    this.states = null;\n\n    // When a value is written, the writer calculates its byte length and puts it into a linked\n    // list of operations to perform when finish() is called. This both allows us to allocate\n    // buffers of the exact required size and reduces the amount of work we have to do compared\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\n    // part is just a linked list walk calling operations with already prepared values.\n}\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup() {\n            return (Writer.create = function create_buffer() {\n                return new BufferWriter();\n            })();\n        }\n        /* istanbul ignore next */\n        : function create_array() {\n            return new Writer();\n        };\n};\n\n/**\n * Creates a new writer.\n * @function\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\n */\nWriter.create = create();\n\n/**\n * Allocates a buffer of the specified size.\n * @param {number} size Buffer size\n * @returns {Uint8Array} Buffer\n */\nWriter.alloc = function alloc(size) {\n    return new util.Array(size);\n};\n\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\n/* istanbul ignore else */\nif (util.Array !== Array)\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\n\n/**\n * Pushes a new operation to the queue.\n * @param {function(Uint8Array, number, *)} fn Function to call\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @returns {Writer} `this`\n * @private\n */\nWriter.prototype._push = function push(fn, len, val) {\n    this.tail = this.tail.next = new Op(fn, len, val);\n    this.len += len;\n    return this;\n};\n\nfunction writeByte(val, buf, pos) {\n    buf[pos] = val & 255;\n}\n\nfunction writeVarint32(val, buf, pos) {\n    while (val > 127) {\n        buf[pos++] = val & 127 | 128;\n        val >>>= 7;\n    }\n    buf[pos] = val;\n}\n\n/**\n * Constructs a new varint writer operation instance.\n * @classdesc Scheduled varint writer operation.\n * @extends Op\n * @constructor\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @ignore\n */\nfunction VarintOp(len, val) {\n    this.len = len;\n    this.next = undefined;\n    this.val = val;\n}\n\nVarintOp.prototype = Object.create(Op.prototype);\nVarintOp.prototype.fn = writeVarint32;\n\n/**\n * Writes an unsigned 32 bit value as a varint.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.uint32 = function write_uint32(value) {\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\n    this.len += (this.tail = this.tail.next = new VarintOp(\n        (value = value >>> 0)\n                < 128       ? 1\n        : value < 16384     ? 2\n        : value < 2097152   ? 3\n        : value < 268435456 ? 4\n        :                     5,\n    value)).len;\n    return this;\n};\n\n/**\n * Writes a signed 32 bit value as a varint.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.int32 = function write_int32(value) {\n    return value < 0\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\n        : this.uint32(value);\n};\n\n/**\n * Writes a 32 bit value as a varint, zig-zag encoded.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sint32 = function write_sint32(value) {\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\n};\n\nfunction writeVarint64(val, buf, pos) {\n    while (val.hi) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\n        val.hi >>>= 7;\n    }\n    while (val.lo > 127) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = val.lo >>> 7;\n    }\n    buf[pos++] = val.lo;\n}\n\n/**\n * Writes an unsigned 64 bit value as a varint.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.uint64 = function write_uint64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a signed 64 bit value as a varint.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.int64 = Writer.prototype.uint64;\n\n/**\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sint64 = function write_sint64(value) {\n    var bits = LongBits.from(value).zzEncode();\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a boolish value as a varint.\n * @param {boolean} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bool = function write_bool(value) {\n    return this._push(writeByte, 1, value ? 1 : 0);\n};\n\nfunction writeFixed32(val, buf, pos) {\n    buf[pos    ] =  val         & 255;\n    buf[pos + 1] =  val >>> 8   & 255;\n    buf[pos + 2] =  val >>> 16  & 255;\n    buf[pos + 3] =  val >>> 24;\n}\n\n/**\n * Writes an unsigned 32 bit value as fixed 32 bits.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.fixed32 = function write_fixed32(value) {\n    return this._push(writeFixed32, 4, value >>> 0);\n};\n\n/**\n * Writes a signed 32 bit value as fixed 32 bits.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\n\n/**\n * Writes an unsigned 64 bit value as fixed 64 bits.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.fixed64 = function write_fixed64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\n};\n\n/**\n * Writes a signed 64 bit value as fixed 64 bits.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\n\n/**\n * Writes a float (32 bit).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.float = function write_float(value) {\n    return this._push(util.float.writeFloatLE, 4, value);\n};\n\n/**\n * Writes a double (64 bit float).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.double = function write_double(value) {\n    return this._push(util.float.writeDoubleLE, 8, value);\n};\n\nvar writeBytes = util.Array.prototype.set\n    ? function writeBytes_set(val, buf, pos) {\n        buf.set(val, pos); // also works for plain array values\n    }\n    /* istanbul ignore next */\n    : function writeBytes_for(val, buf, pos) {\n        for (var i = 0; i < val.length; ++i)\n            buf[pos + i] = val[i];\n    };\n\n/**\n * Writes a sequence of bytes.\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bytes = function write_bytes(value) {\n    var len = value.length >>> 0;\n    if (!len)\n        return this._push(writeByte, 1, 0);\n    if (util.isString(value)) {\n        var buf = Writer.alloc(len = base64.length(value));\n        base64.decode(value, buf, 0);\n        value = buf;\n    }\n    return this.uint32(len)._push(writeBytes, len, value);\n};\n\n/**\n * Writes a string.\n * @param {string} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.string = function write_string(value) {\n    var len = utf8.length(value);\n    return len\n        ? this.uint32(len)._push(utf8.write, len, value)\n        : this._push(writeByte, 1, 0);\n};\n\n/**\n * Forks this writer's state by pushing it to a stack.\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\n * @returns {Writer} `this`\n */\nWriter.prototype.fork = function fork() {\n    this.states = new State(this);\n    this.head = this.tail = new Op(noop, 0, 0);\n    this.len = 0;\n    return this;\n};\n\n/**\n * Resets this instance to the last state.\n * @returns {Writer} `this`\n */\nWriter.prototype.reset = function reset() {\n    if (this.states) {\n        this.head   = this.states.head;\n        this.tail   = this.states.tail;\n        this.len    = this.states.len;\n        this.states = this.states.next;\n    } else {\n        this.head = this.tail = new Op(noop, 0, 0);\n        this.len  = 0;\n    }\n    return this;\n};\n\n/**\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\n * @returns {Writer} `this`\n */\nWriter.prototype.ldelim = function ldelim() {\n    var head = this.head,\n        tail = this.tail,\n        len  = this.len;\n    this.reset().uint32(len);\n    if (len) {\n        this.tail.next = head.next; // skip noop\n        this.tail = tail;\n        this.len += len;\n    }\n    return this;\n};\n\n/**\n * Finishes the write operation.\n * @returns {Uint8Array} Finished buffer\n */\nWriter.prototype.finish = function finish() {\n    var head = this.head.next, // skip noop\n        buf  = this.constructor.alloc(this.len),\n        pos  = 0;\n    while (head) {\n        head.fn(head.val, buf, pos);\n        pos += head.len;\n        head = head.next;\n    }\n    // this.head = this.tail = null;\n    return buf;\n};\n\nWriter._configure = function(BufferWriter_) {\n    BufferWriter = BufferWriter_;\n    Writer.create = create();\n    BufferWriter._configure();\n};\n", "\"use strict\";\nmodule.exports = <PERSON><PERSON>erWriter;\n\n// extends Writer\nvar Writer = require(\"./writer\");\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\n\nvar util = require(\"./util/minimal\");\n\n/**\n * Constructs a new buffer writer instance.\n * @classdesc Wire format writer using node buffers.\n * @extends Writer\n * @constructor\n */\nfunction BufferWriter() {\n    Writer.call(this);\n}\n\nBufferWriter._configure = function () {\n    /**\n     * Allocates a buffer of the specified size.\n     * @function\n     * @param {number} size Buffer size\n     * @returns {Buffer} Buffer\n     */\n    BufferWriter.alloc = util._Buffer_allocUnsafe;\n\n    BufferWriter.writeBytesBuffer = util.Buffer && util.Buffer.prototype instanceof Uint8Array && util.Buffer.prototype.set.name === \"set\"\n        ? function writeBytesBuffer_set(val, buf, pos) {\n          buf.set(val, pos); // faster than copy (requires node >= 4 where <PERSON><PERSON><PERSON> extend Uint8Array and set is properly inherited)\n          // also works for plain array values\n        }\n        /* istanbul ignore next */\n        : function writeBytesBuffer_copy(val, buf, pos) {\n          if (val.copy) // Buffer values\n            val.copy(buf, pos, 0, val.length);\n          else for (var i = 0; i < val.length;) // plain array values\n            buf[pos++] = val[i++];\n        };\n};\n\n\n/**\n * @override\n */\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\n    if (util.isString(value))\n        value = util._Buffer_from(value, \"base64\");\n    var len = value.length >>> 0;\n    this.uint32(len);\n    if (len)\n        this._push(BufferWriter.writeBytesBuffer, len, value);\n    return this;\n};\n\nfunction writeStringBuffer(val, buf, pos) {\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\n        util.utf8.write(val, buf, pos);\n    else if (buf.utf8Write)\n        buf.utf8Write(val, pos);\n    else\n        buf.write(val, pos);\n}\n\n/**\n * @override\n */\nBufferWriter.prototype.string = function write_string_buffer(value) {\n    var len = util.Buffer.byteLength(value);\n    this.uint32(len);\n    if (len)\n        this._push(writeStringBuffer, len, value);\n    return this;\n};\n\n\n/**\n * Finishes the write operation.\n * @name BufferWriter#finish\n * @function\n * @returns {Buffer} Finished buffer\n */\n\nBufferWriter._configure();\n", "\"use strict\";\nmodule.exports = Reader;\n\nvar util      = require(\"./util/minimal\");\n\nvar BufferReader; // cyclic\n\nvar LongBits  = util.LongBits,\n    utf8      = util.utf8;\n\n/* istanbul ignore next */\nfunction indexOutOfRange(reader, writeLength) {\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\n}\n\n/**\n * Constructs a new reader instance using the specified buffer.\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n * @param {Uint8Array} buffer Buffer to read from\n */\nfunction Reader(buffer) {\n\n    /**\n     * Read buffer.\n     * @type {Uint8Array}\n     */\n    this.buf = buffer;\n\n    /**\n     * Read buffer position.\n     * @type {number}\n     */\n    this.pos = 0;\n\n    /**\n     * Read buffer length.\n     * @type {number}\n     */\n    this.len = buffer.length;\n}\n\nvar create_array = typeof Uint8Array !== \"undefined\"\n    ? function create_typed_array(buffer) {\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    }\n    /* istanbul ignore next */\n    : function create_array(buffer) {\n        if (Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    };\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup(buffer) {\n            return (Reader.create = function create_buffer(buffer) {\n                return util.Buffer.isBuffer(buffer)\n                    ? new BufferReader(buffer)\n                    /* istanbul ignore next */\n                    : create_array(buffer);\n            })(buffer);\n        }\n        /* istanbul ignore next */\n        : create_array;\n};\n\n/**\n * Creates a new reader using the specified buffer.\n * @function\n * @param {Uint8Array|Buffer} buffer Buffer to read from\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\n * @throws {Error} If `buffer` is not a valid buffer\n */\nReader.create = create();\n\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\n\n/**\n * Reads a varint as an unsigned 32 bit value.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.uint32 = (function read_uint32_setup() {\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\n    return function read_uint32() {\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\n\n        /* istanbul ignore if */\n        if ((this.pos += 5) > this.len) {\n            this.pos = this.len;\n            throw indexOutOfRange(this, 10);\n        }\n        return value;\n    };\n})();\n\n/**\n * Reads a varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.int32 = function read_int32() {\n    return this.uint32() | 0;\n};\n\n/**\n * Reads a zig-zag encoded varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.sint32 = function read_sint32() {\n    var value = this.uint32();\n    return value >>> 1 ^ -(value & 1) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readLongVarint() {\n    // tends to deopt with local vars for octet etc.\n    var bits = new LongBits(0, 0);\n    var i = 0;\n    if (this.len - this.pos > 4) { // fast route (lo)\n        for (; i < 4; ++i) {\n            // 1st..4th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 5th\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\n        if (this.buf[this.pos++] < 128)\n            return bits;\n        i = 0;\n    } else {\n        for (; i < 3; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 1st..3th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 4th\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\n        return bits;\n    }\n    if (this.len - this.pos > 4) { // fast route (hi)\n        for (; i < 5; ++i) {\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    } else {\n        for (; i < 5; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    }\n    /* istanbul ignore next */\n    throw Error(\"invalid varint encoding\");\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads a varint as a signed 64 bit value.\n * @name Reader#int64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as an unsigned 64 bit value.\n * @name Reader#uint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a zig-zag encoded varint as a signed 64 bit value.\n * @name Reader#sint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as a boolean.\n * @returns {boolean} Value read\n */\nReader.prototype.bool = function read_bool() {\n    return this.uint32() !== 0;\n};\n\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\n    return (buf[end - 4]\n          | buf[end - 3] << 8\n          | buf[end - 2] << 16\n          | buf[end - 1] << 24) >>> 0;\n}\n\n/**\n * Reads fixed 32 bits as an unsigned 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.fixed32 = function read_fixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4);\n};\n\n/**\n * Reads fixed 32 bits as a signed 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.sfixed32 = function read_sfixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readFixed64(/* this: Reader */) {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 8);\n\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads fixed 64 bits.\n * @name Reader#fixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads zig-zag encoded fixed 64 bits.\n * @name Reader#sfixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a float (32 bit) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.float = function read_float() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readFloatLE(this.buf, this.pos);\n    this.pos += 4;\n    return value;\n};\n\n/**\n * Reads a double (64 bit float) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.double = function read_double() {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readDoubleLE(this.buf, this.pos);\n    this.pos += 8;\n    return value;\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @returns {Uint8Array} Value read\n */\nReader.prototype.bytes = function read_bytes() {\n    var length = this.uint32(),\n        start  = this.pos,\n        end    = this.pos + length;\n\n    /* istanbul ignore if */\n    if (end > this.len)\n        throw indexOutOfRange(this, length);\n\n    this.pos += length;\n    if (Array.isArray(this.buf)) // plain array\n        return this.buf.slice(start, end);\n\n    if (start === end) { // fix for IE 10/Win8 and others' subarray returning array of size 1\n        var nativeBuffer = util.Buffer;\n        return nativeBuffer\n            ? nativeBuffer.alloc(0)\n            : new this.buf.constructor(0);\n    }\n    return this._slice.call(this.buf, start, end);\n};\n\n/**\n * Reads a string preceeded by its byte length as a varint.\n * @returns {string} Value read\n */\nReader.prototype.string = function read_string() {\n    var bytes = this.bytes();\n    return utf8.read(bytes, 0, bytes.length);\n};\n\n/**\n * Skips the specified number of bytes if specified, otherwise skips a varint.\n * @param {number} [length] Length if known, otherwise a varint is assumed\n * @returns {Reader} `this`\n */\nReader.prototype.skip = function skip(length) {\n    if (typeof length === \"number\") {\n        /* istanbul ignore if */\n        if (this.pos + length > this.len)\n            throw indexOutOfRange(this, length);\n        this.pos += length;\n    } else {\n        do {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n        } while (this.buf[this.pos++] & 128);\n    }\n    return this;\n};\n\n/**\n * Skips the next element of the specified wire type.\n * @param {number} wireType Wire type received\n * @returns {Reader} `this`\n */\nReader.prototype.skipType = function(wireType) {\n    switch (wireType) {\n        case 0:\n            this.skip();\n            break;\n        case 1:\n            this.skip(8);\n            break;\n        case 2:\n            this.skip(this.uint32());\n            break;\n        case 3:\n            while ((wireType = this.uint32() & 7) !== 4) {\n                this.skipType(wireType);\n            }\n            break;\n        case 5:\n            this.skip(4);\n            break;\n\n        /* istanbul ignore next */\n        default:\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\n    }\n    return this;\n};\n\nReader._configure = function(BufferReader_) {\n    BufferReader = BufferReader_;\n    Reader.create = create();\n    BufferReader._configure();\n\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\n    util.merge(Reader.prototype, {\n\n        int64: function read_int64() {\n            return readLongVarint.call(this)[fn](false);\n        },\n\n        uint64: function read_uint64() {\n            return readLongVarint.call(this)[fn](true);\n        },\n\n        sint64: function read_sint64() {\n            return readLongVarint.call(this).zzDecode()[fn](false);\n        },\n\n        fixed64: function read_fixed64() {\n            return readFixed64.call(this)[fn](true);\n        },\n\n        sfixed64: function read_sfixed64() {\n            return readFixed64.call(this)[fn](false);\n        }\n\n    });\n};\n", "\"use strict\";\nmodule.exports = BufferReader;\n\n// extends Reader\nvar Reader = require(\"./reader\");\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\n\nvar util = require(\"./util/minimal\");\n\n/**\n * Constructs a new buffer reader instance.\n * @classdesc Wire format reader using node buffers.\n * @extends Reader\n * @constructor\n * @param {Buffer} buffer Buffer to read from\n */\nfunction BufferReader(buffer) {\n    Reader.call(this, buffer);\n\n    /**\n     * Read buffer.\n     * @name BufferReader#buf\n     * @type {Buffer}\n     */\n}\n\nBufferReader._configure = function () {\n    /* istanbul ignore else */\n    if (util.Buffer)\n        BufferReader.prototype._slice = util.Buffer.prototype.slice;\n};\n\n\n/**\n * @override\n */\nBufferReader.prototype.string = function read_string_buffer() {\n    var len = this.uint32(); // modifies pos\n    return this.buf.utf8Slice\n        ? this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len))\n        : this.buf.toString(\"utf-8\", this.pos, this.pos = Math.min(this.pos + len, this.len));\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @name BufferReader#bytes\n * @function\n * @returns {Buffer} Value read\n */\n\nBufferReader._configure();\n", "\"use strict\";\nmodule.exports = Service;\n\nvar util = require(\"../util/minimal\");\n\n// Extends EventEmitter\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\n\n/**\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\n *\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\n * @typedef rpc.ServiceMethodCallback\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {Error|null} error Error, if any\n * @param {TRes} [response] Response message\n * @returns {undefined}\n */\n\n/**\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\n * @typedef rpc.ServiceMethod\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\n */\n\n/**\n * Constructs a new RPC service instance.\n * @classdesc An RPC service as returned by {@link Service#create}.\n * @exports rpc.Service\n * @extends util.EventEmitter\n * @constructor\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n */\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\n\n    if (typeof rpcImpl !== \"function\")\n        throw TypeError(\"rpcImpl must be a function\");\n\n    util.EventEmitter.call(this);\n\n    /**\n     * RPC implementation. Becomes `null` once the service is ended.\n     * @type {RPCImpl|null}\n     */\n    this.rpcImpl = rpcImpl;\n\n    /**\n     * Whether requests are length-delimited.\n     * @type {boolean}\n     */\n    this.requestDelimited = Boolean(requestDelimited);\n\n    /**\n     * Whether responses are length-delimited.\n     * @type {boolean}\n     */\n    this.responseDelimited = Boolean(responseDelimited);\n}\n\n/**\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\n * @param {Constructor<TReq>} requestCtor Request constructor\n * @param {Constructor<TRes>} responseCtor Response constructor\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\n * @returns {undefined}\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n */\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\n\n    if (!request)\n        throw TypeError(\"request must be specified\");\n\n    var self = this;\n    if (!callback)\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\n\n    if (!self.rpcImpl) {\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\n        return undefined;\n    }\n\n    try {\n        return self.rpcImpl(\n            method,\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\n            function rpcCallback(err, response) {\n\n                if (err) {\n                    self.emit(\"error\", err, method);\n                    return callback(err);\n                }\n\n                if (response === null) {\n                    self.end(/* endedByRPC */ true);\n                    return undefined;\n                }\n\n                if (!(response instanceof responseCtor)) {\n                    try {\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\n                    } catch (err) {\n                        self.emit(\"error\", err, method);\n                        return callback(err);\n                    }\n                }\n\n                self.emit(\"data\", response, method);\n                return callback(null, response);\n            }\n        );\n    } catch (err) {\n        self.emit(\"error\", err, method);\n        setTimeout(function() { callback(err); }, 0);\n        return undefined;\n    }\n};\n\n/**\n * Ends this service and emits the `end` event.\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\n * @returns {rpc.Service} `this`\n */\nService.prototype.end = function end(endedByRPC) {\n    if (this.rpcImpl) {\n        if (!endedByRPC) // signal end to rpcImpl\n            this.rpcImpl(null, null, null);\n        this.rpcImpl = null;\n        this.emit(\"end\").off();\n    }\n    return this;\n};\n", "\"use strict\";\n\n/**\n * Streaming RPC helpers.\n * @namespace\n */\nvar rpc = exports;\n\n/**\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\n * @typedef RPCImpl\n * @type {function}\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\n * @param {Uint8Array} requestData Request data\n * @param {RPCImplCallback} callback Callback function\n * @returns {undefined}\n * @example\n * function rpcImpl(method, requestData, callback) {\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\n *         throw Error(\"no such method\");\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\n *         callback(err, responseData);\n *     });\n * }\n */\n\n/**\n * Node-style callback as used by {@link RPCImpl}.\n * @typedef RPCImplCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\n * @returns {undefined}\n */\n\nrpc.Service = require(\"./rpc/service\");\n", "\"use strict\";\nmodule.exports = {};\n\n/**\n * Named roots.\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\n * Can also be used manually to make roots available across modules.\n * @name roots\n * @type {Object.<string,Root>}\n * @example\n * // pbjs -r myroot -o compiled.js ...\n *\n * // in another module:\n * require(\"./compiled.js\");\n *\n * // in any subsequent module:\n * var root = protobuf.roots[\"myroot\"];\n */\n", "\"use strict\";\nvar protobuf = exports;\n\n/**\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\n * @name build\n * @type {string}\n * @const\n */\nprotobuf.build = \"minimal\";\n\n// Serialization\nprotobuf.Writer       = require(\"./writer\");\nprotobuf.BufferWriter = require(\"./writer_buffer\");\nprotobuf.Reader       = require(\"./reader\");\nprotobuf.BufferReader = require(\"./reader_buffer\");\n\n// Utility\nprotobuf.util         = require(\"./util/minimal\");\nprotobuf.rpc          = require(\"./rpc\");\nprotobuf.roots        = require(\"./roots\");\nprotobuf.configure    = configure;\n\n/* istanbul ignore next */\n/**\n * Reconfigures the library according to the environment.\n * @returns {undefined}\n */\nfunction configure() {\n    protobuf.util._configure();\n    protobuf.Writer._configure(protobuf.BufferWriter);\n    protobuf.Reader._configure(protobuf.BufferReader);\n}\n\n// Set up buffer utility according to the environment\nconfigure();\n", "// minimal library entry point.\n\n\"use strict\";\nmodule.exports = require(\"./src/index-minimal\");\n"], "mappings": ";;;;;AAAA;AAAA,+FAAAA,UAAAC,SAAA;AAAA;AACA,IAAAA,QAAO,UAAU;AAmBjB,aAAS,UAAU,IAAI,KAAmB;AACtC,UAAI,SAAU,IAAI,MAAM,UAAU,SAAS,CAAC,GACxC,SAAU,GACV,QAAU,GACV,UAAU;AACd,aAAO,QAAQ,UAAU;AACrB,eAAO,QAAQ,IAAI,UAAU,OAAO;AACxC,aAAO,IAAI,QAAQ,SAAS,SAAS,SAAS,QAAQ;AAClD,eAAO,MAAM,IAAI,SAAS,SAAS,KAAmB;AAClD,cAAI,SAAS;AACT,sBAAU;AACV,gBAAI;AACA,qBAAO,GAAG;AAAA,iBACT;AACD,kBAAIC,UAAS,IAAI,MAAM,UAAU,SAAS,CAAC,GACvCC,UAAS;AACb,qBAAOA,UAASD,QAAO;AACnB,gBAAAA,QAAOC,SAAQ,IAAI,UAAUA,OAAM;AACvC,sBAAQ,MAAM,MAAMD,OAAM;AAAA,YAC9B;AAAA,UACJ;AAAA,QACJ;AACA,YAAI;AACA,aAAG,MAAM,OAAO,MAAM,MAAM;AAAA,QAChC,SAAS,KAAK;AACV,cAAI,SAAS;AACT,sBAAU;AACV,mBAAO,GAAG;AAAA,UACd;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AAAA;AAAA;;;ACnDA;AAAA,yFAAAE,UAAA;AAAA;AAOA,QAAI,SAASA;AAOb,WAAO,SAAS,SAAS,OAAO,QAAQ;AACpC,UAAI,IAAI,OAAO;AACf,UAAI,CAAC;AACD,eAAO;AACX,UAAI,IAAI;AACR,aAAO,EAAE,IAAI,IAAI,KAAK,OAAO,OAAO,CAAC,MAAM;AACvC,UAAE;AACN,aAAO,KAAK,KAAK,OAAO,SAAS,CAAC,IAAI,IAAI;AAAA,IAC9C;AAGA,QAAI,MAAM,IAAI,MAAM,EAAE;AAGtB,QAAI,MAAM,IAAI,MAAM,GAAG;AAGvB,SAAS,IAAI,GAAG,IAAI;AAChB,UAAI,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI;AAD5E;AAUT,WAAO,SAAS,SAAS,OAAO,QAAQ,OAAO,KAAK;AAChD,UAAI,QAAQ,MACR,QAAQ,CAAC;AACb,UAAIC,KAAI,GACJ,IAAI,GACJ;AACJ,aAAO,QAAQ,KAAK;AAChB,YAAI,IAAI,OAAO,OAAO;AACtB,gBAAQ,GAAG;AAAA,UACP,KAAK;AACD,kBAAMA,IAAG,IAAI,IAAI,KAAK,CAAC;AACvB,iBAAK,IAAI,MAAM;AACf,gBAAI;AACJ;AAAA,UACJ,KAAK;AACD,kBAAMA,IAAG,IAAI,IAAI,IAAI,KAAK,CAAC;AAC3B,iBAAK,IAAI,OAAO;AAChB,gBAAI;AACJ;AAAA,UACJ,KAAK;AACD,kBAAMA,IAAG,IAAI,IAAI,IAAI,KAAK,CAAC;AAC3B,kBAAMA,IAAG,IAAI,IAAI,IAAI,EAAE;AACvB,gBAAI;AACJ;AAAA,QACR;AACA,YAAIA,KAAI,MAAM;AACV,WAAC,UAAU,QAAQ,CAAC,IAAI,KAAK,OAAO,aAAa,MAAM,QAAQ,KAAK,CAAC;AACrE,UAAAA,KAAI;AAAA,QACR;AAAA,MACJ;AACA,UAAI,GAAG;AACH,cAAMA,IAAG,IAAI,IAAI,CAAC;AAClB,cAAMA,IAAG,IAAI;AACb,YAAI,MAAM;AACN,gBAAMA,IAAG,IAAI;AAAA,MACrB;AACA,UAAI,OAAO;AACP,YAAIA;AACA,gBAAM,KAAK,OAAO,aAAa,MAAM,QAAQ,MAAM,MAAM,GAAGA,EAAC,CAAC,CAAC;AACnE,eAAO,MAAM,KAAK,EAAE;AAAA,MACxB;AACA,aAAO,OAAO,aAAa,MAAM,QAAQ,MAAM,MAAM,GAAGA,EAAC,CAAC;AAAA,IAC9D;AAEA,QAAI,kBAAkB;AAUtB,WAAO,SAAS,SAAS,OAAO,QAAQ,QAAQ,QAAQ;AACpD,UAAI,QAAQ;AACZ,UAAI,IAAI,GACJ;AACJ,eAASA,KAAI,GAAGA,KAAI,OAAO,UAAS;AAChC,YAAI,IAAI,OAAO,WAAWA,IAAG;AAC7B,YAAI,MAAM,MAAM,IAAI;AAChB;AACJ,aAAK,IAAI,IAAI,CAAC,OAAO;AACjB,gBAAM,MAAM,eAAe;AAC/B,gBAAQ,GAAG;AAAA,UACP,KAAK;AACD,gBAAI;AACJ,gBAAI;AACJ;AAAA,UACJ,KAAK;AACD,mBAAO,QAAQ,IAAI,KAAK,KAAK,IAAI,OAAO;AACxC,gBAAI;AACJ,gBAAI;AACJ;AAAA,UACJ,KAAK;AACD,mBAAO,QAAQ,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO;AAC/C,gBAAI;AACJ,gBAAI;AACJ;AAAA,UACJ,KAAK;AACD,mBAAO,QAAQ,KAAK,IAAI,MAAM,IAAI;AAClC,gBAAI;AACJ;AAAA,QACR;AAAA,MACJ;AACA,UAAI,MAAM;AACN,cAAM,MAAM,eAAe;AAC/B,aAAO,SAAS;AAAA,IACpB;AAOA,WAAO,OAAO,SAAS,KAAK,QAAQ;AAChC,aAAO,mEAAmE,KAAK,MAAM;AAAA,IACzF;AAAA;AAAA;;;AC1IA;AAAA,qGAAAC,UAAAC,SAAA;AAAA;AACA,IAAAA,QAAO,UAAU;AAQjB,aAAS,eAAe;AAOpB,WAAK,aAAa,CAAC;AAAA,IACvB;AASA,iBAAa,UAAU,KAAK,SAAS,GAAG,KAAK,IAAI,KAAK;AAClD,OAAC,KAAK,WAAW,GAAG,MAAM,KAAK,WAAW,GAAG,IAAI,CAAC,IAAI,KAAK;AAAA,QACvD;AAAA,QACA,KAAM,OAAO;AAAA,MACjB,CAAC;AACD,aAAO;AAAA,IACX;AAQA,iBAAa,UAAU,MAAM,SAAS,IAAI,KAAK,IAAI;AAC/C,UAAI,QAAQ;AACR,aAAK,aAAa,CAAC;AAAA,WAClB;AACD,YAAI,OAAO;AACP,eAAK,WAAW,GAAG,IAAI,CAAC;AAAA,aACvB;AACD,cAAI,YAAY,KAAK,WAAW,GAAG;AACnC,mBAAS,IAAI,GAAG,IAAI,UAAU;AAC1B,gBAAI,UAAU,CAAC,EAAE,OAAO;AACpB,wBAAU,OAAO,GAAG,CAAC;AAAA;AAErB,gBAAE;AAAA,QACd;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAQA,iBAAa,UAAU,OAAO,SAAS,KAAK,KAAK;AAC7C,UAAI,YAAY,KAAK,WAAW,GAAG;AACnC,UAAI,WAAW;AACX,YAAI,OAAO,CAAC,GACR,IAAI;AACR,eAAO,IAAI,UAAU;AACjB,eAAK,KAAK,UAAU,GAAG,CAAC;AAC5B,aAAK,IAAI,GAAG,IAAI,UAAU;AACtB,oBAAU,CAAC,EAAE,GAAG,MAAM,UAAU,GAAG,EAAE,KAAK,IAAI;AAAA,MACtD;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;AC3EA;AAAA,uFAAAC,UAAAC,SAAA;AAAA;AAEA,IAAAA,QAAO,UAAU,QAAQ,OAAO;AAqFhC,aAAS,QAAQD,UAAS;AAGtB,UAAI,OAAO,iBAAiB,YAAa,EAAC,WAAW;AAEjD,YAAI,MAAM,IAAI,aAAa,CAAE,EAAG,CAAC,GAC7B,MAAM,IAAI,WAAW,IAAI,MAAM,GAC/B,KAAM,IAAI,CAAC,MAAM;AAErB,iBAAS,mBAAmB,KAAK,KAAK,KAAK;AACvC,cAAI,CAAC,IAAI;AACT,cAAI,GAAO,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AAAA,QACxB;AAEA,iBAAS,mBAAmB,KAAK,KAAK,KAAK;AACvC,cAAI,CAAC,IAAI;AACT,cAAI,GAAO,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AAAA,QACxB;AAGA,QAAAA,SAAQ,eAAe,KAAK,qBAAqB;AAEjD,QAAAA,SAAQ,eAAe,KAAK,qBAAqB;AAEjD,iBAAS,kBAAkB,KAAK,KAAK;AACjC,cAAI,CAAC,IAAI,IAAI,GAAO;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,iBAAO,IAAI,CAAC;AAAA,QAChB;AAEA,iBAAS,kBAAkB,KAAK,KAAK;AACjC,cAAI,CAAC,IAAI,IAAI,GAAO;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,iBAAO,IAAI,CAAC;AAAA,QAChB;AAGA,QAAAA,SAAQ,cAAc,KAAK,oBAAoB;AAE/C,QAAAA,SAAQ,cAAc,KAAK,oBAAoB;AAAA,MAGnD,GAAG;AAAA,UAAQ,EAAC,WAAW;AAEnB,iBAAS,mBAAmB,WAAW,KAAK,KAAK,KAAK;AAClD,cAAI,OAAO,MAAM,IAAI,IAAI;AACzB,cAAI;AACA,kBAAM,CAAC;AACX,cAAI,QAAQ;AACR,sBAAU,IAAI,MAAM;AAAA;AAAA,cAAmB;AAAA;AAAA;AAAA,cAAqB;AAAA,eAAY,KAAK,GAAG;AAAA,mBAC3E,MAAM,GAAG;AACd,sBAAU,YAAY,KAAK,GAAG;AAAA,mBACzB,MAAM;AACX,uBAAW,QAAQ,KAAK,gBAAgB,GAAG,KAAK,GAAG;AAAA,mBAC9C,MAAM;AACX,uBAAW,QAAQ,KAAK,KAAK,MAAM,MAAM,oBAAqB,OAAO,GAAG,KAAK,GAAG;AAAA,eAC/E;AACD,gBAAI,WAAW,KAAK,MAAM,KAAK,IAAI,GAAG,IAAI,KAAK,GAAG,GAC9C,WAAW,KAAK,MAAM,MAAM,KAAK,IAAI,GAAG,CAAC,QAAQ,IAAI,OAAO,IAAI;AACpE,uBAAW,QAAQ,KAAK,WAAW,OAAO,KAAK,cAAc,GAAG,KAAK,GAAG;AAAA,UAC5E;AAAA,QACJ;AAEA,QAAAA,SAAQ,eAAe,mBAAmB,KAAK,MAAM,WAAW;AAChE,QAAAA,SAAQ,eAAe,mBAAmB,KAAK,MAAM,WAAW;AAEhE,iBAAS,kBAAkB,UAAU,KAAK,KAAK;AAC3C,cAAI,OAAO,SAAS,KAAK,GAAG,GACxB,QAAQ,QAAQ,MAAM,IAAI,GAC1B,WAAW,SAAS,KAAK,KACzB,WAAW,OAAO;AACtB,iBAAO,aAAa,MACd,WACA,MACA,OAAO,WACP,aAAa,IACb,OAAO,uBAAwB,WAC/B,OAAO,KAAK,IAAI,GAAG,WAAW,GAAG,KAAK,WAAW;AAAA,QAC3D;AAEA,QAAAA,SAAQ,cAAc,kBAAkB,KAAK,MAAM,UAAU;AAC7D,QAAAA,SAAQ,cAAc,kBAAkB,KAAK,MAAM,UAAU;AAAA,MAEjE,GAAG;AAGH,UAAI,OAAO,iBAAiB,YAAa,EAAC,WAAW;AAEjD,YAAI,MAAM,IAAI,aAAa,CAAC,EAAE,CAAC,GAC3B,MAAM,IAAI,WAAW,IAAI,MAAM,GAC/B,KAAM,IAAI,CAAC,MAAM;AAErB,iBAAS,oBAAoB,KAAK,KAAK,KAAK;AACxC,cAAI,CAAC,IAAI;AACT,cAAI,GAAO,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AAAA,QACxB;AAEA,iBAAS,oBAAoB,KAAK,KAAK,KAAK;AACxC,cAAI,CAAC,IAAI;AACT,cAAI,GAAO,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AACpB,cAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AAAA,QACxB;AAGA,QAAAA,SAAQ,gBAAgB,KAAK,sBAAsB;AAEnD,QAAAA,SAAQ,gBAAgB,KAAK,sBAAsB;AAEnD,iBAAS,mBAAmB,KAAK,KAAK;AAClC,cAAI,CAAC,IAAI,IAAI,GAAO;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,iBAAO,IAAI,CAAC;AAAA,QAChB;AAEA,iBAAS,mBAAmB,KAAK,KAAK;AAClC,cAAI,CAAC,IAAI,IAAI,GAAO;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AACpB,iBAAO,IAAI,CAAC;AAAA,QAChB;AAGA,QAAAA,SAAQ,eAAe,KAAK,qBAAqB;AAEjD,QAAAA,SAAQ,eAAe,KAAK,qBAAqB;AAAA,MAGrD,GAAG;AAAA,UAAQ,EAAC,WAAW;AAEnB,iBAAS,oBAAoB,WAAW,MAAM,MAAM,KAAK,KAAK,KAAK;AAC/D,cAAI,OAAO,MAAM,IAAI,IAAI;AACzB,cAAI;AACA,kBAAM,CAAC;AACX,cAAI,QAAQ,GAAG;AACX,sBAAU,GAAG,KAAK,MAAM,IAAI;AAC5B,sBAAU,IAAI,MAAM;AAAA;AAAA,cAAmB;AAAA;AAAA;AAAA,cAAqB;AAAA,eAAY,KAAK,MAAM,IAAI;AAAA,UAC3F,WAAW,MAAM,GAAG,GAAG;AACnB,sBAAU,GAAG,KAAK,MAAM,IAAI;AAC5B,sBAAU,YAAY,KAAK,MAAM,IAAI;AAAA,UACzC,WAAW,MAAM,uBAAyB;AACtC,sBAAU,GAAG,KAAK,MAAM,IAAI;AAC5B,uBAAW,QAAQ,KAAK,gBAAgB,GAAG,KAAK,MAAM,IAAI;AAAA,UAC9D,OAAO;AACH,gBAAI;AACJ,gBAAI,MAAM,wBAAyB;AAC/B,yBAAW,MAAM;AACjB,wBAAU,aAAa,GAAG,KAAK,MAAM,IAAI;AACzC,yBAAW,QAAQ,KAAK,WAAW,gBAAgB,GAAG,KAAK,MAAM,IAAI;AAAA,YACzE,OAAO;AACH,kBAAI,WAAW,KAAK,MAAM,KAAK,IAAI,GAAG,IAAI,KAAK,GAAG;AAClD,kBAAI,aAAa;AACb,2BAAW;AACf,yBAAW,MAAM,KAAK,IAAI,GAAG,CAAC,QAAQ;AACtC,wBAAU,WAAW,qBAAqB,GAAG,KAAK,MAAM,IAAI;AAC5D,yBAAW,QAAQ,KAAK,WAAW,QAAQ,KAAK,WAAW,UAAU,aAAa,GAAG,KAAK,MAAM,IAAI;AAAA,YACxG;AAAA,UACJ;AAAA,QACJ;AAEA,QAAAA,SAAQ,gBAAgB,oBAAoB,KAAK,MAAM,aAAa,GAAG,CAAC;AACxE,QAAAA,SAAQ,gBAAgB,oBAAoB,KAAK,MAAM,aAAa,GAAG,CAAC;AAExE,iBAAS,mBAAmB,UAAU,MAAM,MAAM,KAAK,KAAK;AACxD,cAAI,KAAK,SAAS,KAAK,MAAM,IAAI,GAC7B,KAAK,SAAS,KAAK,MAAM,IAAI;AACjC,cAAI,QAAQ,MAAM,MAAM,IAAI,GACxB,WAAW,OAAO,KAAK,MACvB,WAAW,cAAc,KAAK,WAAW;AAC7C,iBAAO,aAAa,OACd,WACA,MACA,OAAO,WACP,aAAa,IACb,OAAO,SAAS,WAChB,OAAO,KAAK,IAAI,GAAG,WAAW,IAAI,KAAK,WAAW;AAAA,QAC5D;AAEA,QAAAA,SAAQ,eAAe,mBAAmB,KAAK,MAAM,YAAY,GAAG,CAAC;AACrE,QAAAA,SAAQ,eAAe,mBAAmB,KAAK,MAAM,YAAY,GAAG,CAAC;AAAA,MAEzE,GAAG;AAEH,aAAOA;AAAA,IACX;AAIA,aAAS,YAAY,KAAK,KAAK,KAAK;AAChC,UAAI,GAAO,IAAK,MAAa;AAC7B,UAAI,MAAM,CAAC,IAAK,QAAQ,IAAK;AAC7B,UAAI,MAAM,CAAC,IAAK,QAAQ,KAAK;AAC7B,UAAI,MAAM,CAAC,IAAK,QAAQ;AAAA,IAC5B;AAEA,aAAS,YAAY,KAAK,KAAK,KAAK;AAChC,UAAI,GAAO,IAAK,QAAQ;AACxB,UAAI,MAAM,CAAC,IAAK,QAAQ,KAAK;AAC7B,UAAI,MAAM,CAAC,IAAK,QAAQ,IAAK;AAC7B,UAAI,MAAM,CAAC,IAAK,MAAa;AAAA,IACjC;AAEA,aAAS,WAAW,KAAK,KAAK;AAC1B,cAAQ,IAAI,GAAO,IACX,IAAI,MAAM,CAAC,KAAK,IAChB,IAAI,MAAM,CAAC,KAAK,KAChB,IAAI,MAAM,CAAC,KAAK,QAAQ;AAAA,IACpC;AAEA,aAAS,WAAW,KAAK,KAAK;AAC1B,cAAQ,IAAI,GAAO,KAAK,KAChB,IAAI,MAAM,CAAC,KAAK,KAChB,IAAI,MAAM,CAAC,KAAK,IAChB,IAAI,MAAM,CAAC,OAAO;AAAA,IAC9B;AAAA;AAAA;;;AC9UA;AAAA;AAAA;AACA,WAAO,UAAU;AAQjB,aAAS,QAAQ,YAAY;AACzB,UAAI;AACA,YAAI,MAAM,KAAK,QAAQ,QAAQ,KAAI,IAAI,CAAC,EAAE,UAAU;AACpD,YAAI,QAAQ,IAAI,UAAU,OAAO,KAAK,GAAG,EAAE;AACvC,iBAAO;AAAA,MACf,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAAA;AAAA;;;AChBA;AAAA,qFAAAE,UAAA;AAAA;AAOA,QAAI,OAAOA;AAOX,SAAK,SAAS,SAAS,YAAY,QAAQ;AACvC,UAAI,MAAM,GACN,IAAI;AACR,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACpC,YAAI,OAAO,WAAW,CAAC;AACvB,YAAI,IAAI;AACJ,iBAAO;AAAA,iBACF,IAAI;AACT,iBAAO;AAAA,kBACD,IAAI,WAAY,UAAW,OAAO,WAAW,IAAI,CAAC,IAAI,WAAY,OAAQ;AAChF,YAAE;AACF,iBAAO;AAAA,QACX;AACI,iBAAO;AAAA,MACf;AACA,aAAO;AAAA,IACX;AASA,SAAK,OAAO,SAAS,UAAU,QAAQ,OAAO,KAAK;AAC/C,UAAI,MAAM,MAAM;AAChB,UAAI,MAAM;AACN,eAAO;AACX,UAAI,QAAQ,MACR,QAAQ,CAAC,GACT,IAAI,GACJ;AACJ,aAAO,QAAQ,KAAK;AAChB,YAAI,OAAO,OAAO;AAClB,YAAI,IAAI;AACJ,gBAAM,GAAG,IAAI;AAAA,iBACR,IAAI,OAAO,IAAI;AACpB,gBAAM,GAAG,KAAK,IAAI,OAAO,IAAI,OAAO,OAAO,IAAI;AAAA,iBAC1C,IAAI,OAAO,IAAI,KAAK;AACzB,gBAAM,IAAI,MAAM,MAAM,OAAO,OAAO,IAAI,OAAO,MAAM,OAAO,OAAO,IAAI,OAAO,IAAI,OAAO,OAAO,IAAI,MAAM;AAC1G,gBAAM,GAAG,IAAI,SAAU,KAAK;AAC5B,gBAAM,GAAG,IAAI,SAAU,IAAI;AAAA,QAC/B;AACI,gBAAM,GAAG,KAAK,IAAI,OAAO,MAAM,OAAO,OAAO,IAAI,OAAO,IAAI,OAAO,OAAO,IAAI;AAClF,YAAI,IAAI,MAAM;AACV,WAAC,UAAU,QAAQ,CAAC,IAAI,KAAK,OAAO,aAAa,MAAM,QAAQ,KAAK,CAAC;AACrE,cAAI;AAAA,QACR;AAAA,MACJ;AACA,UAAI,OAAO;AACP,YAAI;AACA,gBAAM,KAAK,OAAO,aAAa,MAAM,QAAQ,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC;AACnE,eAAO,MAAM,KAAK,EAAE;AAAA,MACxB;AACA,aAAO,OAAO,aAAa,MAAM,QAAQ,MAAM,MAAM,GAAG,CAAC,CAAC;AAAA,IAC9D;AASA,SAAK,QAAQ,SAAS,WAAW,QAAQ,QAAQ,QAAQ;AACrD,UAAI,QAAQ,QACR,IACA;AACJ,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACpC,aAAK,OAAO,WAAW,CAAC;AACxB,YAAI,KAAK,KAAK;AACV,iBAAO,QAAQ,IAAI;AAAA,QACvB,WAAW,KAAK,MAAM;AAClB,iBAAO,QAAQ,IAAI,MAAM,IAAU;AACnC,iBAAO,QAAQ,IAAI,KAAW,KAAK;AAAA,QACvC,YAAY,KAAK,WAAY,WAAY,KAAK,OAAO,WAAW,IAAI,CAAC,KAAK,WAAY,OAAQ;AAC1F,eAAK,UAAY,KAAK,SAAW,OAAO,KAAK;AAC7C,YAAE;AACF,iBAAO,QAAQ,IAAI,MAAM,KAAU;AACnC,iBAAO,QAAQ,IAAI,MAAM,KAAK,KAAK;AACnC,iBAAO,QAAQ,IAAI,MAAM,IAAK,KAAK;AACnC,iBAAO,QAAQ,IAAI,KAAW,KAAK;AAAA,QACvC,OAAO;AACH,iBAAO,QAAQ,IAAI,MAAM,KAAU;AACnC,iBAAO,QAAQ,IAAI,MAAM,IAAK,KAAK;AACnC,iBAAO,QAAQ,IAAI,KAAW,KAAK;AAAA,QACvC;AAAA,MACJ;AACA,aAAO,SAAS;AAAA,IACpB;AAAA;AAAA;;;ACxGA;AAAA,qFAAAC,UAAAC,SAAA;AAAA;AACA,IAAAA,QAAO,UAAU;AA6BjB,aAAS,KAAK,OAAO,OAAO,MAAM;AAC9B,UAAI,OAAS,QAAQ;AACrB,UAAI,MAAS,SAAS;AACtB,UAAI,OAAS;AACb,UAAI,SAAS;AACb,aAAO,SAAS,WAAWC,OAAM;AAC7B,YAAIA,QAAO,KAAKA,QAAO;AACnB,iBAAO,MAAMA,KAAI;AACrB,YAAI,SAASA,QAAO,MAAM;AACtB,iBAAO,MAAM,IAAI;AACjB,mBAAS;AAAA,QACb;AACA,YAAI,MAAM,MAAM,KAAK,MAAM,QAAQ,UAAUA,KAAI;AACjD,YAAI,SAAS;AACT,oBAAU,SAAS,KAAK;AAC5B,eAAO;AAAA,MACX;AAAA,IACJ;AAAA;AAAA;;;AC/CA;AAAA,qFAAAC,UAAAC,SAAA;AAAA;AACA,IAAAA,QAAO,UAAU;AAEjB,QAAI,OAAO;AAUX,aAAS,SAAS,IAAI,IAAI;AAStB,WAAK,KAAK,OAAO;AAMjB,WAAK,KAAK,OAAO;AAAA,IACrB;AAOA,QAAI,OAAO,SAAS,OAAO,IAAI,SAAS,GAAG,CAAC;AAE5C,SAAK,WAAW,WAAW;AAAE,aAAO;AAAA,IAAG;AACvC,SAAK,WAAW,KAAK,WAAW,WAAW;AAAE,aAAO;AAAA,IAAM;AAC1D,SAAK,SAAS,WAAW;AAAE,aAAO;AAAA,IAAG;AAOrC,QAAI,WAAW,SAAS,WAAW;AAOnC,aAAS,aAAa,SAAS,WAAW,OAAO;AAC7C,UAAI,UAAU;AACV,eAAO;AACX,UAAI,OAAO,QAAQ;AACnB,UAAI;AACA,gBAAQ,CAAC;AACb,UAAI,KAAK,UAAU,GACf,MAAM,QAAQ,MAAM,eAAe;AACvC,UAAI,MAAM;AACN,aAAK,CAAC,OAAO;AACb,aAAK,CAAC,OAAO;AACb,YAAI,EAAE,KAAK,YAAY;AACnB,eAAK;AACL,cAAI,EAAE,KAAK;AACP,iBAAK;AAAA,QACb;AAAA,MACJ;AACA,aAAO,IAAI,SAAS,IAAI,EAAE;AAAA,IAC9B;AAOA,aAAS,OAAO,SAAS,KAAK,OAAO;AACjC,UAAI,OAAO,UAAU;AACjB,eAAO,SAAS,WAAW,KAAK;AACpC,UAAI,KAAK,SAAS,KAAK,GAAG;AAEtB,YAAI,KAAK;AACL,kBAAQ,KAAK,KAAK,WAAW,KAAK;AAAA;AAElC,iBAAO,SAAS,WAAW,SAAS,OAAO,EAAE,CAAC;AAAA,MACtD;AACA,aAAO,MAAM,OAAO,MAAM,OAAO,IAAI,SAAS,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,IAAI;AAAA,IACvF;AAOA,aAAS,UAAU,WAAW,SAAS,SAAS,UAAU;AACtD,UAAI,CAAC,YAAY,KAAK,OAAO,IAAI;AAC7B,YAAI,KAAK,CAAC,KAAK,KAAK,MAAM,GACtB,KAAK,CAAC,KAAK,OAAW;AAC1B,YAAI,CAAC;AACD,eAAK,KAAK,MAAM;AACpB,eAAO,EAAE,KAAK,KAAK;AAAA,MACvB;AACA,aAAO,KAAK,KAAK,KAAK,KAAK;AAAA,IAC/B;AAOA,aAAS,UAAU,SAAS,SAAS,OAAO,UAAU;AAClD,aAAO,KAAK,OACN,IAAI,KAAK,KAAK,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,QAAQ,QAAQ,CAAC,IAEzD,EAAE,KAAK,KAAK,KAAK,GAAG,MAAM,KAAK,KAAK,GAAG,UAAU,QAAQ,QAAQ,EAAE;AAAA,IAC7E;AAEA,QAAI,aAAa,OAAO,UAAU;AAOlC,aAAS,WAAW,SAAS,SAAS,MAAM;AACxC,UAAI,SAAS;AACT,eAAO;AACX,aAAO,IAAI;AAAA,SACL,WAAW,KAAK,MAAM,CAAC,IACvB,WAAW,KAAK,MAAM,CAAC,KAAK,IAC5B,WAAW,KAAK,MAAM,CAAC,KAAK,KAC5B,WAAW,KAAK,MAAM,CAAC,KAAK,QAAQ;AAAA,SAEpC,WAAW,KAAK,MAAM,CAAC,IACvB,WAAW,KAAK,MAAM,CAAC,KAAK,IAC5B,WAAW,KAAK,MAAM,CAAC,KAAK,KAC5B,WAAW,KAAK,MAAM,CAAC,KAAK,QAAQ;AAAA,MAC1C;AAAA,IACJ;AAMA,aAAS,UAAU,SAAS,SAAS,SAAS;AAC1C,aAAO,OAAO;AAAA,QACV,KAAK,KAAY;AAAA,QACjB,KAAK,OAAO,IAAK;AAAA,QACjB,KAAK,OAAO,KAAK;AAAA,QACjB,KAAK,OAAO;AAAA,QACZ,KAAK,KAAY;AAAA,QACjB,KAAK,OAAO,IAAK;AAAA,QACjB,KAAK,OAAO,KAAK;AAAA,QACjB,KAAK,OAAO;AAAA,MAChB;AAAA,IACJ;AAMA,aAAS,UAAU,WAAW,SAAS,WAAW;AAC9C,UAAI,OAAS,KAAK,MAAM;AACxB,WAAK,OAAQ,KAAK,MAAM,IAAI,KAAK,OAAO,MAAM,UAAU;AACxD,WAAK,MAAQ,KAAK,MAAM,IAAsB,UAAU;AACxD,aAAO;AAAA,IACX;AAMA,aAAS,UAAU,WAAW,SAAS,WAAW;AAC9C,UAAI,OAAO,EAAE,KAAK,KAAK;AACvB,WAAK,OAAQ,KAAK,OAAO,IAAI,KAAK,MAAM,MAAM,UAAU;AACxD,WAAK,MAAQ,KAAK,OAAO,IAAqB,UAAU;AACxD,aAAO;AAAA,IACX;AAMA,aAAS,UAAU,SAAS,SAAS,SAAS;AAC1C,UAAI,QAAS,KAAK,IACd,SAAS,KAAK,OAAO,KAAK,KAAK,MAAM,OAAO,GAC5C,QAAS,KAAK,OAAO;AACzB,aAAO,UAAU,IACV,UAAU,IACR,QAAQ,QACN,QAAQ,MAAM,IAAI,IAClB,QAAQ,UAAU,IAAI,IACxB,QAAQ,QACN,QAAQ,MAAM,IAAI,IAClB,QAAQ,UAAU,IAAI,IAC1B,QAAQ,MAAM,IAAI;AAAA,IAC7B;AAAA;AAAA;;;ACvMA;AAAA,oFAAAC,UAAA;AAAA;AACA,QAAI,OAAOA;AAGX,SAAK,YAAY;AAGjB,SAAK,SAAS;AAGd,SAAK,eAAe;AAGpB,SAAK,QAAQ;AAGb,SAAK,UAAU;AAGf,SAAK,OAAO;AAGZ,SAAK,OAAO;AAGZ,SAAK,WAAW;AAOhB,SAAK,SAAS,QAAQ,OAAO,WAAW,eAClB,UACA,OAAO,WACP,OAAO,QAAQ,YACf,OAAO,QAAQ,SAAS,IAAI;AAOlD,SAAK,SAAS,KAAK,UAAU,UACf,OAAO,WAAW,eAAe,UACjC,OAAO,SAAW,eAAe,QACjCA;AAQd,SAAK,aAAa,OAAO,SAAS,OAAO,OAAO,CAAC,CAAC;AAAA;AAAA,MAA+B,CAAC;AAAA;AAOlF,SAAK,cAAc,OAAO,SAAS,OAAO,OAAO,CAAC,CAAC;AAAA;AAAA,MAA+B,CAAC;AAAA;AAQnF,SAAK,YAAY,OAAO;AAAA,IAAwC,SAAS,UAAU,OAAO;AACtF,aAAO,OAAO,UAAU,YAAY,SAAS,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM;AAAA,IACjF;AAOA,SAAK,WAAW,SAAS,SAAS,OAAO;AACrC,aAAO,OAAO,UAAU,YAAY,iBAAiB;AAAA,IACzD;AAOA,SAAK,WAAW,SAAS,SAAS,OAAO;AACrC,aAAO,SAAS,OAAO,UAAU;AAAA,IACrC;AAUA,SAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQL,KAAK,QAAQ,SAAS,MAAM,KAAK,MAAM;AACnC,UAAI,QAAQ,IAAI,IAAI;AACpB,UAAI,SAAS,QAAQ,IAAI,eAAe,IAAI;AACxC,eAAO,OAAO,UAAU,aAAa,MAAM,QAAQ,KAAK,IAAI,MAAM,SAAS,OAAO,KAAK,KAAK,EAAE,UAAU;AAC5G,aAAO;AAAA,IACX;AAaA,SAAK,UAAU,WAAW;AACtB,UAAI;AACA,YAAI,SAAS,KAAK,QAAQ,QAAQ,EAAE;AAEpC,eAAO,OAAO,UAAU,YAAY;AAAA;AAAA,UAAoC;AAAA;AAAA,MAC5E,SAAS,GAAG;AAER,eAAO;AAAA,MACX;AAAA,IACJ,GAAG;AAGH,SAAK,eAAe;AAGpB,SAAK,sBAAsB;AAO3B,SAAK,YAAY,SAAS,UAAU,aAAa;AAE7C,aAAO,OAAO,gBAAgB,WACxB,KAAK,SACD,KAAK,oBAAoB,WAAW,IACpC,IAAI,KAAK,MAAM,WAAW,IAC9B,KAAK,SACD,KAAK,aAAa,WAAW,IAC7B,OAAO,eAAe,cAClB,cACA,IAAI,WAAW,WAAW;AAAA,IAC5C;AAMA,SAAK,QAAQ,OAAO,eAAe,cAAc,aAAwC;AAezF,SAAK;AAAA,IAAkC,KAAK,OAAO;AAAA,IAAsC,KAAK,OAAO,QAAQ;AAAA,IACtE,KAAK,OAAO,QACvC,KAAK,QAAQ,MAAM;AAO/B,SAAK,SAAS;AAOd,SAAK,UAAU;AAOf,SAAK,UAAU;AAOf,SAAK,aAAa,SAAS,WAAW,OAAO;AACzC,aAAO,QACD,KAAK,SAAS,KAAK,KAAK,EAAE,OAAO,IACjC,KAAK,SAAS;AAAA,IACxB;AAQA,SAAK,eAAe,SAAS,aAAa,MAAM,UAAU;AACtD,UAAI,OAAO,KAAK,SAAS,SAAS,IAAI;AACtC,UAAI,KAAK;AACL,eAAO,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK,IAAI,QAAQ;AACxD,aAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,IAC1C;AAUA,aAAS,MAAM,KAAK,KAAK,UAAU;AAC/B,eAAS,OAAO,OAAO,KAAK,GAAG,GAAG,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE;AACxD,YAAI,IAAI,KAAK,CAAC,CAAC,MAAM,UAAa,CAAC;AAC/B,cAAI,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;AAClC,aAAO;AAAA,IACX;AAEA,SAAK,QAAQ;AAOb,SAAK,UAAU,SAAS,QAAQ,KAAK;AACjC,aAAO,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,UAAU,CAAC;AAAA,IACxD;AAQA,aAAS,SAAS,MAAM;AAEpB,eAAS,YAAY,SAAS,YAAY;AAEtC,YAAI,EAAE,gBAAgB;AAClB,iBAAO,IAAI,YAAY,SAAS,UAAU;AAK9C,eAAO,eAAe,MAAM,WAAW,EAAE,KAAK,WAAW;AAAE,iBAAO;AAAA,QAAS,EAAE,CAAC;AAG9E,YAAI,MAAM;AACN,gBAAM,kBAAkB,MAAM,WAAW;AAAA;AAEzC,iBAAO,eAAe,MAAM,SAAS,EAAE,OAAO,IAAI,MAAM,EAAE,SAAS,GAAG,CAAC;AAE3E,YAAI;AACA,gBAAM,MAAM,UAAU;AAAA,MAC9B;AAEA,kBAAY,YAAY,OAAO,OAAO,MAAM,WAAW;AAAA,QACnD,aAAa;AAAA,UACT,OAAO;AAAA,UACP,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB;AAAA,QACA,MAAM;AAAA,UACF,KAAK,SAAS,MAAM;AAAE,mBAAO;AAAA,UAAM;AAAA,UACnC,KAAK;AAAA,UACL,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,UAKZ,cAAc;AAAA,QAClB;AAAA,QACA,UAAU;AAAA,UACN,OAAO,SAAS,QAAQ;AAAE,mBAAO,KAAK,OAAO,OAAO,KAAK;AAAA,UAAS;AAAA,UAClE,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB;AAAA,MACJ,CAAC;AAED,aAAO;AAAA,IACX;AAEA,SAAK,WAAW;AAmBhB,SAAK,gBAAgB,SAAS,eAAe;AAoB7C,SAAK,cAAc,SAAS,SAAS,YAAY;AAC7C,UAAI,WAAW,CAAC;AAChB,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE;AACrC,iBAAS,WAAW,CAAC,CAAC,IAAI;AAO9B,aAAO,WAAW;AACd,iBAAS,OAAO,OAAO,KAAK,IAAI,GAAGC,KAAI,KAAK,SAAS,GAAGA,KAAI,IAAI,EAAEA;AAC9D,cAAI,SAAS,KAAKA,EAAC,CAAC,MAAM,KAAK,KAAK,KAAKA,EAAC,CAAC,MAAM,UAAa,KAAK,KAAKA,EAAC,CAAC,MAAM;AAC5E,mBAAO,KAAKA,EAAC;AAAA,MACzB;AAAA,IACJ;AAeA,SAAK,cAAc,SAAS,SAAS,YAAY;AAQ7C,aAAO,SAAS,MAAM;AAClB,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE;AACrC,cAAI,WAAW,CAAC,MAAM;AAClB,mBAAO,KAAK,WAAW,CAAC,CAAC;AAAA,MACrC;AAAA,IACJ;AAkBA,SAAK,gBAAgB;AAAA,MACjB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACV;AAGA,SAAK,aAAa,WAAW;AACzB,UAAI,SAAS,KAAK;AAElB,UAAI,CAAC,QAAQ;AACT,aAAK,eAAe,KAAK,sBAAsB;AAC/C;AAAA,MACJ;AAGA,WAAK,eAAe,OAAO,SAAS,WAAW,QAAQ,OAAO;AAAA,MAE1D,SAAS,YAAY,OAAO,UAAU;AAClC,eAAO,IAAI,OAAO,OAAO,QAAQ;AAAA,MACrC;AACJ,WAAK,sBAAsB,OAAO;AAAA,MAE9B,SAAS,mBAAmB,MAAM;AAC9B,eAAO,IAAI,OAAO,IAAI;AAAA,MAC1B;AAAA,IACR;AAAA;AAAA;;;ACrbA;AAAA,8EAAAC,UAAAC,SAAA;AAAA;AACA,IAAAA,QAAO,UAAU;AAEjB,QAAI,OAAY;AAEhB,QAAI;AAEJ,QAAI,WAAY,KAAK;AAArB,QACI,SAAY,KAAK;AADrB,QAEI,OAAY,KAAK;AAWrB,aAAS,GAAG,IAAI,KAAK,KAAK;AAMtB,WAAK,KAAK;AAMV,WAAK,MAAM;AAMX,WAAK,OAAO;AAMZ,WAAK,MAAM;AAAA,IACf;AAGA,aAAS,OAAO;AAAA,IAAC;AAUjB,aAAS,MAAM,QAAQ;AAMnB,WAAK,OAAO,OAAO;AAMnB,WAAK,OAAO,OAAO;AAMnB,WAAK,MAAM,OAAO;AAMlB,WAAK,OAAO,OAAO;AAAA,IACvB;AAOA,aAAS,SAAS;AAMd,WAAK,MAAM;AAMX,WAAK,OAAO,IAAI,GAAG,MAAM,GAAG,CAAC;AAM7B,WAAK,OAAO,KAAK;AAMjB,WAAK,SAAS;AAAA,IAOlB;AAEA,QAAI,SAAS,SAASC,UAAS;AAC3B,aAAO,KAAK,SACN,SAAS,sBAAsB;AAC7B,gBAAQ,OAAO,SAAS,SAAS,gBAAgB;AAC7C,iBAAO,IAAI,aAAa;AAAA,QAC5B,GAAG;AAAA,MACP,IAEE,SAAS,eAAe;AACtB,eAAO,IAAI,OAAO;AAAA,MACtB;AAAA,IACR;AAOA,WAAO,SAAS,OAAO;AAOvB,WAAO,QAAQ,SAAS,MAAM,MAAM;AAChC,aAAO,IAAI,KAAK,MAAM,IAAI;AAAA,IAC9B;AAIA,QAAI,KAAK,UAAU;AACf,aAAO,QAAQ,KAAK,KAAK,OAAO,OAAO,KAAK,MAAM,UAAU,QAAQ;AAUxE,WAAO,UAAU,QAAQ,SAAS,KAAK,IAAI,KAAK,KAAK;AACjD,WAAK,OAAO,KAAK,KAAK,OAAO,IAAI,GAAG,IAAI,KAAK,GAAG;AAChD,WAAK,OAAO;AACZ,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,KAAK,KAAK,KAAK;AAC9B,UAAI,GAAG,IAAI,MAAM;AAAA,IACrB;AAEA,aAAS,cAAc,KAAK,KAAK,KAAK;AAClC,aAAO,MAAM,KAAK;AACd,YAAI,KAAK,IAAI,MAAM,MAAM;AACzB,iBAAS;AAAA,MACb;AACA,UAAI,GAAG,IAAI;AAAA,IACf;AAWA,aAAS,SAAS,KAAK,KAAK;AACxB,WAAK,MAAM;AACX,WAAK,OAAO;AACZ,WAAK,MAAM;AAAA,IACf;AAEA,aAAS,YAAY,OAAO,OAAO,GAAG,SAAS;AAC/C,aAAS,UAAU,KAAK;AAOxB,WAAO,UAAU,SAAS,SAAS,aAAa,OAAO;AAGnD,WAAK,QAAQ,KAAK,OAAO,KAAK,KAAK,OAAO,IAAI;AAAA,SACzC,QAAQ,UAAU,KACT,MAAY,IACpB,QAAQ,QAAY,IACpB,QAAQ,UAAY,IACpB,QAAQ,YAAY,IACA;AAAA,QAC1B;AAAA,MAAK,GAAG;AACR,aAAO;AAAA,IACX;AAQA,WAAO,UAAU,QAAQ,SAAS,YAAY,OAAO;AACjD,aAAO,QAAQ,IACT,KAAK,MAAM,eAAe,IAAI,SAAS,WAAW,KAAK,CAAC,IACxD,KAAK,OAAO,KAAK;AAAA,IAC3B;AAOA,WAAO,UAAU,SAAS,SAAS,aAAa,OAAO;AACnD,aAAO,KAAK,QAAQ,SAAS,IAAI,SAAS,QAAQ,CAAC;AAAA,IACvD;AAEA,aAAS,cAAc,KAAK,KAAK,KAAK;AAClC,aAAO,IAAI,IAAI;AACX,YAAI,KAAK,IAAI,IAAI,KAAK,MAAM;AAC5B,YAAI,MAAM,IAAI,OAAO,IAAI,IAAI,MAAM,QAAQ;AAC3C,YAAI,QAAQ;AAAA,MAChB;AACA,aAAO,IAAI,KAAK,KAAK;AACjB,YAAI,KAAK,IAAI,IAAI,KAAK,MAAM;AAC5B,YAAI,KAAK,IAAI,OAAO;AAAA,MACxB;AACA,UAAI,KAAK,IAAI,IAAI;AAAA,IACrB;AAQA,WAAO,UAAU,SAAS,SAAS,aAAa,OAAO;AACnD,UAAI,OAAO,SAAS,KAAK,KAAK;AAC9B,aAAO,KAAK,MAAM,eAAe,KAAK,OAAO,GAAG,IAAI;AAAA,IACxD;AASA,WAAO,UAAU,QAAQ,OAAO,UAAU;AAQ1C,WAAO,UAAU,SAAS,SAAS,aAAa,OAAO;AACnD,UAAI,OAAO,SAAS,KAAK,KAAK,EAAE,SAAS;AACzC,aAAO,KAAK,MAAM,eAAe,KAAK,OAAO,GAAG,IAAI;AAAA,IACxD;AAOA,WAAO,UAAU,OAAO,SAAS,WAAW,OAAO;AAC/C,aAAO,KAAK,MAAM,WAAW,GAAG,QAAQ,IAAI,CAAC;AAAA,IACjD;AAEA,aAAS,aAAa,KAAK,KAAK,KAAK;AACjC,UAAI,GAAO,IAAK,MAAc;AAC9B,UAAI,MAAM,CAAC,IAAK,QAAQ,IAAM;AAC9B,UAAI,MAAM,CAAC,IAAK,QAAQ,KAAM;AAC9B,UAAI,MAAM,CAAC,IAAK,QAAQ;AAAA,IAC5B;AAOA,WAAO,UAAU,UAAU,SAAS,cAAc,OAAO;AACrD,aAAO,KAAK,MAAM,cAAc,GAAG,UAAU,CAAC;AAAA,IAClD;AAQA,WAAO,UAAU,WAAW,OAAO,UAAU;AAQ7C,WAAO,UAAU,UAAU,SAAS,cAAc,OAAO;AACrD,UAAI,OAAO,SAAS,KAAK,KAAK;AAC9B,aAAO,KAAK,MAAM,cAAc,GAAG,KAAK,EAAE,EAAE,MAAM,cAAc,GAAG,KAAK,EAAE;AAAA,IAC9E;AASA,WAAO,UAAU,WAAW,OAAO,UAAU;AAQ7C,WAAO,UAAU,QAAQ,SAAS,YAAY,OAAO;AACjD,aAAO,KAAK,MAAM,KAAK,MAAM,cAAc,GAAG,KAAK;AAAA,IACvD;AAQA,WAAO,UAAU,SAAS,SAAS,aAAa,OAAO;AACnD,aAAO,KAAK,MAAM,KAAK,MAAM,eAAe,GAAG,KAAK;AAAA,IACxD;AAEA,QAAI,aAAa,KAAK,MAAM,UAAU,MAChC,SAAS,eAAe,KAAK,KAAK,KAAK;AACrC,UAAI,IAAI,KAAK,GAAG;AAAA,IACpB,IAEE,SAAS,eAAe,KAAK,KAAK,KAAK;AACrC,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE;AAC9B,YAAI,MAAM,CAAC,IAAI,IAAI,CAAC;AAAA,IAC5B;AAOJ,WAAO,UAAU,QAAQ,SAAS,YAAY,OAAO;AACjD,UAAI,MAAM,MAAM,WAAW;AAC3B,UAAI,CAAC;AACD,eAAO,KAAK,MAAM,WAAW,GAAG,CAAC;AACrC,UAAI,KAAK,SAAS,KAAK,GAAG;AACtB,YAAI,MAAM,OAAO,MAAM,MAAM,OAAO,OAAO,KAAK,CAAC;AACjD,eAAO,OAAO,OAAO,KAAK,CAAC;AAC3B,gBAAQ;AAAA,MACZ;AACA,aAAO,KAAK,OAAO,GAAG,EAAE,MAAM,YAAY,KAAK,KAAK;AAAA,IACxD;AAOA,WAAO,UAAU,SAAS,SAAS,aAAa,OAAO;AACnD,UAAI,MAAM,KAAK,OAAO,KAAK;AAC3B,aAAO,MACD,KAAK,OAAO,GAAG,EAAE,MAAM,KAAK,OAAO,KAAK,KAAK,IAC7C,KAAK,MAAM,WAAW,GAAG,CAAC;AAAA,IACpC;AAOA,WAAO,UAAU,OAAO,SAAS,OAAO;AACpC,WAAK,SAAS,IAAI,MAAM,IAAI;AAC5B,WAAK,OAAO,KAAK,OAAO,IAAI,GAAG,MAAM,GAAG,CAAC;AACzC,WAAK,MAAM;AACX,aAAO;AAAA,IACX;AAMA,WAAO,UAAU,QAAQ,SAAS,QAAQ;AACtC,UAAI,KAAK,QAAQ;AACb,aAAK,OAAS,KAAK,OAAO;AAC1B,aAAK,OAAS,KAAK,OAAO;AAC1B,aAAK,MAAS,KAAK,OAAO;AAC1B,aAAK,SAAS,KAAK,OAAO;AAAA,MAC9B,OAAO;AACH,aAAK,OAAO,KAAK,OAAO,IAAI,GAAG,MAAM,GAAG,CAAC;AACzC,aAAK,MAAO;AAAA,MAChB;AACA,aAAO;AAAA,IACX;AAMA,WAAO,UAAU,SAAS,SAAS,SAAS;AACxC,UAAI,OAAO,KAAK,MACZ,OAAO,KAAK,MACZ,MAAO,KAAK;AAChB,WAAK,MAAM,EAAE,OAAO,GAAG;AACvB,UAAI,KAAK;AACL,aAAK,KAAK,OAAO,KAAK;AACtB,aAAK,OAAO;AACZ,aAAK,OAAO;AAAA,MAChB;AACA,aAAO;AAAA,IACX;AAMA,WAAO,UAAU,SAAS,SAAS,SAAS;AACxC,UAAI,OAAO,KAAK,KAAK,MACjB,MAAO,KAAK,YAAY,MAAM,KAAK,GAAG,GACtC,MAAO;AACX,aAAO,MAAM;AACT,aAAK,GAAG,KAAK,KAAK,KAAK,GAAG;AAC1B,eAAO,KAAK;AACZ,eAAO,KAAK;AAAA,MAChB;AAEA,aAAO;AAAA,IACX;AAEA,WAAO,aAAa,SAAS,eAAe;AACxC,qBAAe;AACf,aAAO,SAAS,OAAO;AACvB,mBAAa,WAAW;AAAA,IAC5B;AAAA;AAAA;;;AChdA;AAAA,qFAAAC,UAAAC,SAAA;AAAA;AACA,IAAAA,QAAO,UAAU;AAGjB,QAAI,SAAS;AACb,KAAC,aAAa,YAAY,OAAO,OAAO,OAAO,SAAS,GAAG,cAAc;AAEzE,QAAI,OAAO;AAQX,aAAS,eAAe;AACpB,aAAO,KAAK,IAAI;AAAA,IACpB;AAEA,iBAAa,aAAa,WAAY;AAOlC,mBAAa,QAAQ,KAAK;AAE1B,mBAAa,mBAAmB,KAAK,UAAU,KAAK,OAAO,qBAAqB,cAAc,KAAK,OAAO,UAAU,IAAI,SAAS,QAC3H,SAAS,qBAAqB,KAAK,KAAK,KAAK;AAC7C,YAAI,IAAI,KAAK,GAAG;AAAA,MAElB,IAEE,SAAS,sBAAsB,KAAK,KAAK,KAAK;AAC9C,YAAI,IAAI;AACN,cAAI,KAAK,KAAK,KAAK,GAAG,IAAI,MAAM;AAAA,YAC7B,UAAS,IAAI,GAAG,IAAI,IAAI;AAC3B,cAAI,KAAK,IAAI,IAAI,GAAG;AAAA,MACxB;AAAA,IACR;AAMA,iBAAa,UAAU,QAAQ,SAAS,mBAAmB,OAAO;AAC9D,UAAI,KAAK,SAAS,KAAK;AACnB,gBAAQ,KAAK,aAAa,OAAO,QAAQ;AAC7C,UAAI,MAAM,MAAM,WAAW;AAC3B,WAAK,OAAO,GAAG;AACf,UAAI;AACA,aAAK,MAAM,aAAa,kBAAkB,KAAK,KAAK;AACxD,aAAO;AAAA,IACX;AAEA,aAAS,kBAAkB,KAAK,KAAK,KAAK;AACtC,UAAI,IAAI,SAAS;AACb,aAAK,KAAK,MAAM,KAAK,KAAK,GAAG;AAAA,eACxB,IAAI;AACT,YAAI,UAAU,KAAK,GAAG;AAAA;AAEtB,YAAI,MAAM,KAAK,GAAG;AAAA,IAC1B;AAKA,iBAAa,UAAU,SAAS,SAAS,oBAAoB,OAAO;AAChE,UAAI,MAAM,KAAK,OAAO,WAAW,KAAK;AACtC,WAAK,OAAO,GAAG;AACf,UAAI;AACA,aAAK,MAAM,mBAAmB,KAAK,KAAK;AAC5C,aAAO;AAAA,IACX;AAUA,iBAAa,WAAW;AAAA;AAAA;;;ACpFxB;AAAA,8EAAAC,UAAAC,SAAA;AAAA;AACA,IAAAA,QAAO,UAAU;AAEjB,QAAI,OAAY;AAEhB,QAAI;AAEJ,QAAI,WAAY,KAAK;AAArB,QACI,OAAY,KAAK;AAGrB,aAAS,gBAAgB,QAAQ,aAAa;AAC1C,aAAO,WAAW,yBAAyB,OAAO,MAAM,SAAS,eAAe,KAAK,QAAQ,OAAO,GAAG;AAAA,IAC3G;AAQA,aAAS,OAAO,QAAQ;AAMpB,WAAK,MAAM;AAMX,WAAK,MAAM;AAMX,WAAK,MAAM,OAAO;AAAA,IACtB;AAEA,QAAI,eAAe,OAAO,eAAe,cACnC,SAAS,mBAAmB,QAAQ;AAClC,UAAI,kBAAkB,cAAc,MAAM,QAAQ,MAAM;AACpD,eAAO,IAAI,OAAO,MAAM;AAC5B,YAAM,MAAM,gBAAgB;AAAA,IAChC,IAEE,SAASC,cAAa,QAAQ;AAC5B,UAAI,MAAM,QAAQ,MAAM;AACpB,eAAO,IAAI,OAAO,MAAM;AAC5B,YAAM,MAAM,gBAAgB;AAAA,IAChC;AAEJ,QAAI,SAAS,SAASC,UAAS;AAC3B,aAAO,KAAK,SACN,SAAS,oBAAoB,QAAQ;AACnC,gBAAQ,OAAO,SAAS,SAAS,cAAcC,SAAQ;AACnD,iBAAO,KAAK,OAAO,SAASA,OAAM,IAC5B,IAAI,aAAaA,OAAM,IAEvB,aAAaA,OAAM;AAAA,QAC7B,GAAG,MAAM;AAAA,MACb,IAEE;AAAA,IACV;AASA,WAAO,SAAS,OAAO;AAEvB,WAAO,UAAU,SAAS,KAAK,MAAM,UAAU;AAAA,IAAuC,KAAK,MAAM,UAAU;AAO3G,WAAO,UAAU,SAAU,0BAAS,oBAAoB;AACpD,UAAI,QAAQ;AACZ,aAAO,SAAS,cAAc;AAC1B,iBAAkB,KAAK,IAAI,KAAK,GAAG,IAAI,SAAgB;AAAG,YAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAK,QAAO;AACjG,iBAAS,SAAS,KAAK,IAAI,KAAK,GAAG,IAAI,QAAS,OAAO;AAAG,YAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAK,QAAO;AACjG,iBAAS,SAAS,KAAK,IAAI,KAAK,GAAG,IAAI,QAAQ,QAAQ;AAAG,YAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAK,QAAO;AACjG,iBAAS,SAAS,KAAK,IAAI,KAAK,GAAG,IAAI,QAAQ,QAAQ;AAAG,YAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAK,QAAO;AACjG,iBAAS,SAAS,KAAK,IAAI,KAAK,GAAG,IAAK,OAAO,QAAQ;AAAG,YAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAK,QAAO;AAGjG,aAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AAC5B,eAAK,MAAM,KAAK;AAChB,gBAAM,gBAAgB,MAAM,EAAE;AAAA,QAClC;AACA,eAAO;AAAA,MACX;AAAA,IACJ,GAAG;AAMH,WAAO,UAAU,QAAQ,SAAS,aAAa;AAC3C,aAAO,KAAK,OAAO,IAAI;AAAA,IAC3B;AAMA,WAAO,UAAU,SAAS,SAAS,cAAc;AAC7C,UAAI,QAAQ,KAAK,OAAO;AACxB,aAAO,UAAU,IAAI,EAAE,QAAQ,KAAK;AAAA,IACxC;AAIA,aAAS,iBAAiB;AAEtB,UAAI,OAAO,IAAI,SAAS,GAAG,CAAC;AAC5B,UAAI,IAAI;AACR,UAAI,KAAK,MAAM,KAAK,MAAM,GAAG;AACzB,eAAO,IAAI,GAAG,EAAE,GAAG;AAEf,eAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,GAAG,IAAI,QAAQ,IAAI,OAAO;AAC9D,cAAI,KAAK,IAAI,KAAK,KAAK,IAAI;AACvB,mBAAO;AAAA,QACf;AAEA,aAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,GAAG,IAAI,QAAQ,QAAQ;AAC3D,aAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,GAAG,IAAI,QAAS,OAAO;AAC3D,YAAI,KAAK,IAAI,KAAK,KAAK,IAAI;AACvB,iBAAO;AACX,YAAI;AAAA,MACR,OAAO;AACH,eAAO,IAAI,GAAG,EAAE,GAAG;AAEf,cAAI,KAAK,OAAO,KAAK;AACjB,kBAAM,gBAAgB,IAAI;AAE9B,eAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,GAAG,IAAI,QAAQ,IAAI,OAAO;AAC9D,cAAI,KAAK,IAAI,KAAK,KAAK,IAAI;AACvB,mBAAO;AAAA,QACf;AAEA,aAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK,IAAI,QAAQ,IAAI,OAAO;AAChE,eAAO;AAAA,MACX;AACA,UAAI,KAAK,MAAM,KAAK,MAAM,GAAG;AACzB,eAAO,IAAI,GAAG,EAAE,GAAG;AAEf,eAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,GAAG,IAAI,QAAQ,IAAI,IAAI,OAAO;AAClE,cAAI,KAAK,IAAI,KAAK,KAAK,IAAI;AACvB,mBAAO;AAAA,QACf;AAAA,MACJ,OAAO;AACH,eAAO,IAAI,GAAG,EAAE,GAAG;AAEf,cAAI,KAAK,OAAO,KAAK;AACjB,kBAAM,gBAAgB,IAAI;AAE9B,eAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,GAAG,IAAI,QAAQ,IAAI,IAAI,OAAO;AAClE,cAAI,KAAK,IAAI,KAAK,KAAK,IAAI;AACvB,mBAAO;AAAA,QACf;AAAA,MACJ;AAEA,YAAM,MAAM,yBAAyB;AAAA,IACzC;AA6BA,WAAO,UAAU,OAAO,SAAS,YAAY;AACzC,aAAO,KAAK,OAAO,MAAM;AAAA,IAC7B;AAEA,aAAS,gBAAgB,KAAK,KAAK;AAC/B,cAAQ,IAAI,MAAM,CAAC,IACX,IAAI,MAAM,CAAC,KAAK,IAChB,IAAI,MAAM,CAAC,KAAK,KAChB,IAAI,MAAM,CAAC,KAAK,QAAQ;AAAA,IACpC;AAMA,WAAO,UAAU,UAAU,SAAS,eAAe;AAG/C,UAAI,KAAK,MAAM,IAAI,KAAK;AACpB,cAAM,gBAAgB,MAAM,CAAC;AAEjC,aAAO,gBAAgB,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAClD;AAMA,WAAO,UAAU,WAAW,SAAS,gBAAgB;AAGjD,UAAI,KAAK,MAAM,IAAI,KAAK;AACpB,cAAM,gBAAgB,MAAM,CAAC;AAEjC,aAAO,gBAAgB,KAAK,KAAK,KAAK,OAAO,CAAC,IAAI;AAAA,IACtD;AAIA,aAAS,cAAgC;AAGrC,UAAI,KAAK,MAAM,IAAI,KAAK;AACpB,cAAM,gBAAgB,MAAM,CAAC;AAEjC,aAAO,IAAI,SAAS,gBAAgB,KAAK,KAAK,KAAK,OAAO,CAAC,GAAG,gBAAgB,KAAK,KAAK,KAAK,OAAO,CAAC,CAAC;AAAA,IAC1G;AAuBA,WAAO,UAAU,QAAQ,SAAS,aAAa;AAG3C,UAAI,KAAK,MAAM,IAAI,KAAK;AACpB,cAAM,gBAAgB,MAAM,CAAC;AAEjC,UAAI,QAAQ,KAAK,MAAM,YAAY,KAAK,KAAK,KAAK,GAAG;AACrD,WAAK,OAAO;AACZ,aAAO;AAAA,IACX;AAOA,WAAO,UAAU,SAAS,SAAS,cAAc;AAG7C,UAAI,KAAK,MAAM,IAAI,KAAK;AACpB,cAAM,gBAAgB,MAAM,CAAC;AAEjC,UAAI,QAAQ,KAAK,MAAM,aAAa,KAAK,KAAK,KAAK,GAAG;AACtD,WAAK,OAAO;AACZ,aAAO;AAAA,IACX;AAMA,WAAO,UAAU,QAAQ,SAAS,aAAa;AAC3C,UAAI,SAAS,KAAK,OAAO,GACrB,QAAS,KAAK,KACd,MAAS,KAAK,MAAM;AAGxB,UAAI,MAAM,KAAK;AACX,cAAM,gBAAgB,MAAM,MAAM;AAEtC,WAAK,OAAO;AACZ,UAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,eAAO,KAAK,IAAI,MAAM,OAAO,GAAG;AAEpC,UAAI,UAAU,KAAK;AACf,YAAI,eAAe,KAAK;AACxB,eAAO,eACD,aAAa,MAAM,CAAC,IACpB,IAAI,KAAK,IAAI,YAAY,CAAC;AAAA,MACpC;AACA,aAAO,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,IAChD;AAMA,WAAO,UAAU,SAAS,SAAS,cAAc;AAC7C,UAAI,QAAQ,KAAK,MAAM;AACvB,aAAO,KAAK,KAAK,OAAO,GAAG,MAAM,MAAM;AAAA,IAC3C;AAOA,WAAO,UAAU,OAAO,SAAS,KAAK,QAAQ;AAC1C,UAAI,OAAO,WAAW,UAAU;AAE5B,YAAI,KAAK,MAAM,SAAS,KAAK;AACzB,gBAAM,gBAAgB,MAAM,MAAM;AACtC,aAAK,OAAO;AAAA,MAChB,OAAO;AACH,WAAG;AAEC,cAAI,KAAK,OAAO,KAAK;AACjB,kBAAM,gBAAgB,IAAI;AAAA,QAClC,SAAS,KAAK,IAAI,KAAK,KAAK,IAAI;AAAA,MACpC;AACA,aAAO;AAAA,IACX;AAOA,WAAO,UAAU,WAAW,SAAS,UAAU;AAC3C,cAAQ,UAAU;AAAA,QACd,KAAK;AACD,eAAK,KAAK;AACV;AAAA,QACJ,KAAK;AACD,eAAK,KAAK,CAAC;AACX;AAAA,QACJ,KAAK;AACD,eAAK,KAAK,KAAK,OAAO,CAAC;AACvB;AAAA,QACJ,KAAK;AACD,kBAAQ,WAAW,KAAK,OAAO,IAAI,OAAO,GAAG;AACzC,iBAAK,SAAS,QAAQ;AAAA,UAC1B;AACA;AAAA,QACJ,KAAK;AACD,eAAK,KAAK,CAAC;AACX;AAAA;AAAA,QAGJ;AACI,gBAAM,MAAM,uBAAuB,WAAW,gBAAgB,KAAK,GAAG;AAAA,MAC9E;AACA,aAAO;AAAA,IACX;AAEA,WAAO,aAAa,SAAS,eAAe;AACxC,qBAAe;AACf,aAAO,SAAS,OAAO;AACvB,mBAAa,WAAW;AAExB,UAAI,KAAK,KAAK,OAAO;AAAA;AAAA,QAAsC;AAAA;AAC3D,WAAK,MAAM,OAAO,WAAW;AAAA,QAEzB,OAAO,SAAS,aAAa;AACzB,iBAAO,eAAe,KAAK,IAAI,EAAE,EAAE,EAAE,KAAK;AAAA,QAC9C;AAAA,QAEA,QAAQ,SAAS,cAAc;AAC3B,iBAAO,eAAe,KAAK,IAAI,EAAE,EAAE,EAAE,IAAI;AAAA,QAC7C;AAAA,QAEA,QAAQ,SAAS,cAAc;AAC3B,iBAAO,eAAe,KAAK,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK;AAAA,QACzD;AAAA,QAEA,SAAS,SAAS,eAAe;AAC7B,iBAAO,YAAY,KAAK,IAAI,EAAE,EAAE,EAAE,IAAI;AAAA,QAC1C;AAAA,QAEA,UAAU,SAAS,gBAAgB;AAC/B,iBAAO,YAAY,KAAK,IAAI,EAAE,EAAE,EAAE,KAAK;AAAA,QAC3C;AAAA,MAEJ,CAAC;AAAA,IACL;AAAA;AAAA;;;AC/ZA;AAAA,qFAAAC,UAAAC,SAAA;AAAA;AACA,IAAAA,QAAO,UAAU;AAGjB,QAAI,SAAS;AACb,KAAC,aAAa,YAAY,OAAO,OAAO,OAAO,SAAS,GAAG,cAAc;AAEzE,QAAI,OAAO;AASX,aAAS,aAAa,QAAQ;AAC1B,aAAO,KAAK,MAAM,MAAM;AAAA,IAO5B;AAEA,iBAAa,aAAa,WAAY;AAElC,UAAI,KAAK;AACL,qBAAa,UAAU,SAAS,KAAK,OAAO,UAAU;AAAA,IAC9D;AAMA,iBAAa,UAAU,SAAS,SAAS,qBAAqB;AAC1D,UAAI,MAAM,KAAK,OAAO;AACtB,aAAO,KAAK,IAAI,YACV,KAAK,IAAI,UAAU,KAAK,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,MAAM,KAAK,KAAK,GAAG,CAAC,IAC1E,KAAK,IAAI,SAAS,SAAS,KAAK,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,MAAM,KAAK,KAAK,GAAG,CAAC;AAAA,IAC5F;AASA,iBAAa,WAAW;AAAA;AAAA;;;AClDxB;AAAA,mFAAAC,UAAAC,SAAA;AAAA;AACA,IAAAA,QAAO,UAAU;AAEjB,QAAI,OAAO;AAGX,KAAC,QAAQ,YAAY,OAAO,OAAO,KAAK,aAAa,SAAS,GAAG,cAAc;AAmC/E,aAAS,QAAQ,SAAS,kBAAkB,mBAAmB;AAE3D,UAAI,OAAO,YAAY;AACnB,cAAM,UAAU,4BAA4B;AAEhD,WAAK,aAAa,KAAK,IAAI;AAM3B,WAAK,UAAU;AAMf,WAAK,mBAAmB,QAAQ,gBAAgB;AAMhD,WAAK,oBAAoB,QAAQ,iBAAiB;AAAA,IACtD;AAaA,YAAQ,UAAU,UAAU,SAAS,QAAQ,QAAQ,aAAa,cAAc,SAAS,UAAU;AAE/F,UAAI,CAAC;AACD,cAAM,UAAU,2BAA2B;AAE/C,UAAIC,QAAO;AACX,UAAI,CAAC;AACD,eAAO,KAAK,UAAU,SAASA,OAAM,QAAQ,aAAa,cAAc,OAAO;AAEnF,UAAI,CAACA,MAAK,SAAS;AACf,mBAAW,WAAW;AAAE,mBAAS,MAAM,eAAe,CAAC;AAAA,QAAG,GAAG,CAAC;AAC9D,eAAO;AAAA,MACX;AAEA,UAAI;AACA,eAAOA,MAAK;AAAA,UACR;AAAA,UACA,YAAYA,MAAK,mBAAmB,oBAAoB,QAAQ,EAAE,OAAO,EAAE,OAAO;AAAA,UAClF,SAAS,YAAY,KAAK,UAAU;AAEhC,gBAAI,KAAK;AACL,cAAAA,MAAK,KAAK,SAAS,KAAK,MAAM;AAC9B,qBAAO,SAAS,GAAG;AAAA,YACvB;AAEA,gBAAI,aAAa,MAAM;AACnB,cAAAA,MAAK;AAAA;AAAA,gBAAqB;AAAA,cAAI;AAC9B,qBAAO;AAAA,YACX;AAEA,gBAAI,EAAE,oBAAoB,eAAe;AACrC,kBAAI;AACA,2BAAW,aAAaA,MAAK,oBAAoB,oBAAoB,QAAQ,EAAE,QAAQ;AAAA,cAC3F,SAASC,MAAK;AACV,gBAAAD,MAAK,KAAK,SAASC,MAAK,MAAM;AAC9B,uBAAO,SAASA,IAAG;AAAA,cACvB;AAAA,YACJ;AAEA,YAAAD,MAAK,KAAK,QAAQ,UAAU,MAAM;AAClC,mBAAO,SAAS,MAAM,QAAQ;AAAA,UAClC;AAAA,QACJ;AAAA,MACJ,SAAS,KAAK;AACV,QAAAA,MAAK,KAAK,SAAS,KAAK,MAAM;AAC9B,mBAAW,WAAW;AAAE,mBAAS,GAAG;AAAA,QAAG,GAAG,CAAC;AAC3C,eAAO;AAAA,MACX;AAAA,IACJ;AAOA,YAAQ,UAAU,MAAM,SAAS,IAAI,YAAY;AAC7C,UAAI,KAAK,SAAS;AACd,YAAI,CAAC;AACD,eAAK,QAAQ,MAAM,MAAM,IAAI;AACjC,aAAK,UAAU;AACf,aAAK,KAAK,KAAK,EAAE,IAAI;AAAA,MACzB;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;AC7IA;AAAA,2EAAAE,UAAA;AAAA;AAMA,QAAI,MAAMA;AA6BV,QAAI,UAAU;AAAA;AAAA;;;ACnCd;AAAA,6EAAAC,UAAAC,SAAA;AAAA;AACA,IAAAA,QAAO,UAAU,CAAC;AAAA;AAAA;;;ACDlB;AAAA,qFAAAC,UAAA;AAAA;AACA,QAAI,WAAWA;AAQf,aAAS,QAAQ;AAGjB,aAAS,SAAe;AACxB,aAAS,eAAe;AACxB,aAAS,SAAe;AACxB,aAAS,eAAe;AAGxB,aAAS,OAAe;AACxB,aAAS,MAAe;AACxB,aAAS,QAAe;AACxB,aAAS,YAAe;AAOxB,aAAS,YAAY;AACjB,eAAS,KAAK,WAAW;AACzB,eAAS,OAAO,WAAW,SAAS,YAAY;AAChD,eAAS,OAAO,WAAW,SAAS,YAAY;AAAA,IACpD;AAGA,cAAU;AAAA;AAAA;;;ACnCV,IAAAC,mBAAA;AAAA,2EAAAC,UAAAC,SAAA;AAGA,IAAAA,QAAO,UAAU;AAAA;AAAA;", "names": ["exports", "module", "params", "offset", "exports", "i", "exports", "module", "exports", "module", "exports", "exports", "module", "size", "exports", "module", "exports", "i", "exports", "module", "create", "exports", "module", "exports", "module", "create_array", "create", "buffer", "exports", "module", "exports", "module", "self", "err", "exports", "exports", "module", "exports", "require_minimal", "exports", "module"]}