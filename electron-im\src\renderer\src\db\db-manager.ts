// # 本地数据库相关 (IndexedDB)
// # 数据库管理，提供加密和CRUD接口

import { encryptObject, decryptObject, type EncryptedData } from '../utils/crypto-utils'
import type { Message } from '../store/message'

// 数据库配置
const DB_CONFIG = {
  name: 'ElectronIMDB',
  version: 1,
  stores: {
    messages: 'messages'
  }
}

// 存储在IndexedDB中的加密消息结构
interface EncryptedMessage {
  id: string
  chatUserId: string // 聊天对象的用户ID
  encryptedData: EncryptedData
  timestamp: number
  createdAt: number
}

// 消息查询选项
interface MessageQueryOptions {
  limit?: number
  offset?: number
  startTime?: number
  endTime?: number
}

/**
 * IndexedDB 数据库管理器
 * 提供消息的加密存储和查询功能
 */
export class DatabaseManager {
  private db: IDBDatabase | null = null
  private currentUserId: string | null = null

  /**
   * 初始化数据库
   */
  async initialize(userId: string): Promise<void> {
    this.currentUserId = userId

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_CONFIG.name, DB_CONFIG.version)

      request.onerror = () => {
        console.error('数据库打开失败:', request.error)
        reject(new Error(`数据库初始化失败: ${request.error?.message}`))
      }

      request.onsuccess = () => {
        this.db = request.result
        console.log('数据库初始化成功')
        resolve()
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result

        // 创建消息存储
        if (!db.objectStoreNames.contains(DB_CONFIG.stores.messages)) {
          const messageStore = db.createObjectStore(DB_CONFIG.stores.messages, {
            keyPath: 'id'
          })

          // 创建索引
          messageStore.createIndex('chatUserId', 'chatUserId', { unique: false })
          messageStore.createIndex('timestamp', 'timestamp', { unique: false })
          messageStore.createIndex('chatUserIdTimestamp', ['chatUserId', 'timestamp'], {
            unique: false
          })

          console.log('消息存储创建成功')
        }
      }
    })
  }

  /**
   * 存储消息（加密）
   */
  async storeMessage(message: Message, chatUserId: string): Promise<void> {
    if (!this.db || !this.currentUserId) {
      throw new Error('数据库未初始化')
    }

    try {
      // 加密消息内容
      const encryptedData = await encryptObject(message, this.currentUserId)

      const encryptedMessage: EncryptedMessage = {
        id: message.id,
        chatUserId,
        encryptedData,
        timestamp: message.timestamp,
        createdAt: Date.now()
      }

      return new Promise((resolve, reject) => {
        const transaction = this.db!.transaction([DB_CONFIG.stores.messages], 'readwrite')
        const store = transaction.objectStore(DB_CONFIG.stores.messages)

        const request = store.put(encryptedMessage)

        request.onsuccess = () => {
          console.log(`消息已加密存储: ${message.id}`)
          resolve()
        }

        request.onerror = () => {
          console.error('消息存储失败:', request.error)
          reject(new Error(`消息存储失败: ${request.error?.message}`))
        }
      })
    } catch (error) {
      console.error('消息加密失败:', error)
      throw error
    }
  }

  /**
   * 批量存储消息
   */
  async storeMessages(messages: Message[], chatUserId: string): Promise<void> {
    if (!this.db || !this.currentUserId) {
      throw new Error('数据库未初始化')
    }

    const transaction = this.db.transaction([DB_CONFIG.stores.messages], 'readwrite')
    const store = transaction.objectStore(DB_CONFIG.stores.messages)

    const promises = messages.map(async (message) => {
      try {
        const encryptedData = await encryptObject(message, this.currentUserId!)

        const encryptedMessage: EncryptedMessage = {
          id: message.id,
          chatUserId,
          encryptedData,
          timestamp: message.timestamp,
          createdAt: Date.now()
        }

        return new Promise<void>((resolve, reject) => {
          const request = store.put(encryptedMessage)
          request.onsuccess = () => resolve()
          request.onerror = () => reject(request.error)
        })
      } catch (error) {
        console.error(`消息加密失败 ${message.id}:`, error)
        throw error
      }
    })

    try {
      await Promise.all(promises)
      console.log(`批量存储 ${messages.length} 条消息成功`)
    } catch (error) {
      console.error('批量存储消息失败:', error)
      throw error
    }
  }

  /**
   * 获取聊天消息（解密）
   */
  async getMessages(chatUserId: string, options: MessageQueryOptions = {}): Promise<Message[]> {
    if (!this.db || !this.currentUserId) {
      throw new Error('数据库未初始化')
    }

    const { limit = 50, offset = 0 } = options

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([DB_CONFIG.stores.messages], 'readonly')
      const store = transaction.objectStore(DB_CONFIG.stores.messages)
      const index = store.index('chatUserIdTimestamp')

      // 创建查询范围
      const range = IDBKeyRange.bound([chatUserId, 0], [chatUserId, Date.now()])
      const request = index.openCursor(range, 'prev') // 按时间倒序

      const results: EncryptedMessage[] = []
      let count = 0
      let skipped = 0

      request.onsuccess = () => {
        const cursor = request.result

        if (cursor && count < limit) {
          if (skipped < offset) {
            skipped++
            cursor.continue()
            return
          }

          results.push(cursor.value)
          count++
          cursor.continue()
        } else {
          // 解密消息
          this.decryptMessages(results)
            .then((messages) => {
              console.log(`从数据库获取 ${messages.length} 条消息 (用户: ${chatUserId})`)
              resolve(messages)
            })
            .catch(reject)
        }
      }

      request.onerror = () => {
        console.error('查询消息失败:', request.error)
        reject(new Error(`查询消息失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 检查是否存在聊天记录
   */
  async hasMessages(chatUserId: string): Promise<boolean> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([DB_CONFIG.stores.messages], 'readonly')
      const store = transaction.objectStore(DB_CONFIG.stores.messages)
      const index = store.index('chatUserId')

      const request = index.count(chatUserId)

      request.onsuccess = () => {
        resolve(request.result > 0)
      }

      request.onerror = () => {
        console.error('检查消息存在性失败:', request.error)
        reject(new Error(`检查消息存在性失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 删除聊天记录
   */
  async deleteMessages(chatUserId: string): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([DB_CONFIG.stores.messages], 'readwrite')
      const store = transaction.objectStore(DB_CONFIG.stores.messages)
      const index = store.index('chatUserId')

      const request = index.openCursor(chatUserId)

      request.onsuccess = () => {
        const cursor = request.result
        if (cursor) {
          cursor.delete()
          cursor.continue()
        } else {
          console.log(`删除用户 ${chatUserId} 的所有消息`)
          resolve()
        }
      }

      request.onerror = () => {
        console.error('删除消息失败:', request.error)
        reject(new Error(`删除消息失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 清空所有数据
   */
  async clearAll(): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([DB_CONFIG.stores.messages], 'readwrite')
      const store = transaction.objectStore(DB_CONFIG.stores.messages)

      const request = store.clear()

      request.onsuccess = () => {
        console.log('所有消息已清空')
        resolve()
      }

      request.onerror = () => {
        console.error('清空消息失败:', request.error)
        reject(new Error(`清空消息失败: ${request.error?.message}`))
      }
    })
  }

  /**
   * 解密消息列表
   */
  private async decryptMessages(encryptedMessages: EncryptedMessage[]): Promise<Message[]> {
    if (!this.currentUserId) {
      throw new Error('用户未登录')
    }

    const promises = encryptedMessages.map(async (encMsg) => {
      try {
        return await decryptObject<Message>(encMsg.encryptedData, this.currentUserId!)
      } catch (error) {
        console.error(`解密消息失败 ${encMsg.id}:`, error)
        // 返回一个错误消息对象，而不是抛出异常
        return {
          id: encMsg.id,
          senderId: 'system',
          receiverId: 'system',
          content: '[消息解密失败]',
          timestamp: encMsg.timestamp,
          type: 1
        } as Message
      }
    })

    return Promise.all(promises)
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      this.db.close()
      this.db = null
      this.currentUserId = null
      console.log('数据库连接已关闭')
    }
  }
}

// 导出单例实例
export const dbManager = new DatabaseManager()
